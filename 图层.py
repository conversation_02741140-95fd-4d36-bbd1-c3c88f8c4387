import uiautomator2 as u2
import xml.etree.ElementTree as ET

# 连接到当前设备
d = u2.connect()

# 获取屏幕上的元素信息并解析父子关系
def print_all_elements_info():
    # 获取UI元素的结构
    xml = d.dump_hierarchy()  # 使用 dump_hierarchy() 获取 UI 层级信息

    # 解析XML
    root = ET.fromstring(xml)

    # 输出屏幕上的所有元素及父子关系
    print("屏幕上的所有元素信息:")
    print_elements_info(root)
    print("\n树形结构输出:")
    print_elements_tree(root)

def print_elements_info(element):
    """
    输出每个元素的详细信息：资源ID、类别、坐标和文本
    """
    # 获取当前元素的属性
    attrib = element.attrib
    resource_id = attrib.get('resource-id', '无 ID')
    class_name = attrib.get('class', '无类名')
    bounds = attrib.get('bounds', '无坐标')
    text = attrib.get('text', '无文本')

    print(f"元素: {resource_id}, 类别: {class_name}, 坐标: {bounds}, 文本: {text}")

    # 如果有子元素，递归输出它们的信息
    for child in element:
        print_elements_info(child)

def print_elements_tree(element, level=0, is_last_child=False):
    """
    递归打印元素信息及父子关系，以树形结构显示
    :param element: 当前 XML 元素
    :param level: 当前层级，用于缩进
    :param is_last_child: 是否为当前层级的最后一个子元素，用于决定如何输出
    """
    # 获取当前元素的属性
    attrib = element.attrib
    indent = "│   " * level  # 每个级别缩进 4 个空格
    child_marker = "└── " if is_last_child else "├── "
    
    # 获取资源ID和文本
    resource_id = attrib.get('resource-id', '无 ID')
    text = attrib.get('text', '无文本')
    
    # 输出元素的信息（包括文本信息）
    print(f"{indent}{child_marker}{resource_id} ({text})")

    # 如果有子元素，递归输出
    children = list(element)
    for i, child in enumerate(children):
        print_elements_tree(child, level + 1, is_last_child=(i == len(children) - 1))

# 调用函数打印屏幕元素信息
print_all_elements_info()
