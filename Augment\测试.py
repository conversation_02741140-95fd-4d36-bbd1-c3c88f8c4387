import uiautomator2 as u2
import tkinter as tk
from tkinter import messagebox, ttk
import time
import os
import subprocess
import re
import xml.etree.ElementTree as ET
from lxml import etree



# 尝试导入PIL库，用于截图预览
TRY_PIL = True
try:
    from PIL import Image, ImageTk
except ImportError:
    TRY_PIL = False

class UIAutomatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("uiautomator2 自动化工具")
        self.root.geometry("480x640")  # 设置窗口大小为480*640
        self.d = None

        # 创建桌面上的"测试导出"文件夹来存储截图和其他文件
        self.output_dir = os.path.join(os.path.join(os.path.expanduser("~"), "Desktop"), "测试导出")
        if not os.path.exists(self.output_dir):
            try:
                os.makedirs(self.output_dir)
            except:
                self.output_dir = os.getcwd()  # 如果创建失败，使用当前目录



        # 设备连接部分
        self.setup_connection_frame()

        # 元素定位部分
        self.setup_element_frame()

        # 滑动操作部分
        self.setup_swipe_frame()

        # 系统按键部分（新增）
        self.setup_key_frame()

        # 操作按钮部分
        self.setup_action_frame()

        # 日志输出部分
        self.setup_log_frame()

        # 状态栏
        self.setup_status_bar()

    def setup_connection_frame(self):
        frame = tk.LabelFrame(self.root, text="设备连接", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        tk.Label(frame, text="设备ID/IP:").grid(row=0, column=0)
        self.device_entry = tk.Entry(frame, width=30)
        self.device_entry.grid(row=0, column=1)
        self.device_entry.insert(0, "自动连接")

        tk.Button(frame, text="连接设备", command=self.connect_device).grid(row=0, column=2, padx=5)

    def setup_element_frame(self):
        frame = tk.LabelFrame(self.root, text="元素定位", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        # 定位方式选择
        self.locate_method = tk.StringVar(value="text")
        methods = ["text", "resourceId", "coordinates", "className", "xpath"]
        method_labels = ["文本", "资源ID", "坐标", "类名", "路径"]

        # 创建一个容器框架，使用pack确保间距一致
        method_frame = tk.Frame(frame)
        method_frame.grid(row=0, column=0, columnspan=5, sticky=tk.W)

        for i, (method, label) in enumerate(zip(methods, method_labels)):
            tk.Radiobutton(method_frame, text=label, variable=self.locate_method,
                          value=method, command=self.update_element_placeholder).pack(side=tk.LEFT, padx=10)

        # 元素取值输入框
        tk.Label(frame, text="元素取值:").grid(row=1, column=0)
        self.element_entry = tk.Entry(frame, width=40)
        self.element_entry.grid(row=1, column=1, columnspan=4, sticky=tk.EW)

        # 默认值为文本类型的示例
        self.element_entry.insert(0, "请输入要定位的文本内容")

        # 添加等待超时设置
        tk.Label(frame, text="等待超时(秒):").grid(row=2, column=0)
        self.wait_timeout = tk.Entry(frame, width=5)
        self.wait_timeout.grid(row=2, column=1)
        self.wait_timeout.insert(0, "10")

        # 只保留等待元素出现按钮
        tk.Button(frame, text="等待元素出现", command=self.wait_element).grid(row=2, column=2, padx=5)

    def setup_swipe_frame(self):
        frame = tk.LabelFrame(self.root, text="滑动操作", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        # 滑动方向选择
        self.swipe_direction = tk.StringVar(value="up")
        direction_frame = tk.Frame(frame)
        direction_frame.grid(row=0, column=0, columnspan=4, sticky=tk.W)

        # 使用pack而不grid，确保间距一致
        tk.Radiobutton(direction_frame, text="向上滑动", variable=self.swipe_direction, value="up").pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(direction_frame, text="向下滑动", variable=self.swipe_direction, value="down").pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(direction_frame, text="向左滑动", variable=self.swipe_direction, value="left").pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(direction_frame, text="向右滑动", variable=self.swipe_direction, value="right").pack(side=tk.LEFT, padx=10)

        # 滑动参数
        tk.Label(frame, text="滑动时间(秒):").grid(row=1, column=0)
        self.swipe_duration = tk.Entry(frame, width=5)
        self.swipe_duration.grid(row=1, column=1)
        self.swipe_duration.insert(0, "1.0")

    def setup_key_frame(self):  # 新增系统按键区域
        frame = tk.LabelFrame(self.root, text="系统按键", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        tk.Button(frame, text="返回键", command=self.press_back).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="Home键", command=self.press_home).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="最近任务", command=self.press_recent).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="电源键", command=self.press_power).pack(side=tk.LEFT, padx=5)

    def update_element_placeholder(self):
        # 根据选择的定位方式更新输入框的提示文本
        method = self.locate_method.get()
        self.element_entry.delete(0, tk.END)  # 清空当前内容

        if method == "text":
            self.element_entry.insert(0, "请输入文本内容")
        elif method == "resourceId":
            self.element_entry.insert(0, "com.example.app:id/element_id")
        elif method == "coordinates":
            self.element_entry.insert(0, "360,720")
        elif method == "xpath":
            self.element_entry.insert(0, "//*[@text='文本内容']")
        elif method == "className":
            self.element_entry.insert(0, "android.widget.TextView")

    def setup_action_frame(self):
        frame = tk.LabelFrame(self.root, text="操作", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        # 恢复原有布局，保留所有按钮
        tk.Button(frame, text="点击元素", command=self.click_element).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="执行滑动", command=self.do_swipe).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="查看UI树", command=self.dump_hierarchy).pack(side=tk.LEFT, padx=5)

    def setup_log_frame(self):
        frame = tk.LabelFrame(self.root, text="日志输出", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(frame, height=10, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

    def log(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.config(state=tk.DISABLED)
        self.log_text.see(tk.END)

    def connect_device(self):
        device_id = self.device_entry.get().strip()
        try:
            self.d = u2.connect(device_id if device_id != "自动连接" else None)
            device_info = self.d.info

            # 只获取需要的信息
            package_name = self.d.app_current().get('package', '未知包名')
            display_height = device_info.get('displayHeight', '未知')
            display_width = device_info.get('displayWidth', '未知')

            # 获取设备型号 - 尝试多种方式
            model = device_info.get('model', '')
            if not model:
                model = device_info.get('productName', '')
            if not model:
                try:
                    model = self.d.shell('getprop ro.product.model').strip()
                except:
                    pass
            if not model:
                try:
                    model = self.d.shell('getprop ro.product.device').strip()
                except:
                    pass
            if not model:
                model = '未知型号'

            # 使用辅助方法获取SN码
            sn = self.get_device_sn()

            # 构建简化的设备信息字符串
            device_info_str = f"应用包名: {package_name}\n屏幕高度: {display_height}\n屏幕宽度: {display_width}\n设备型号: {model}\nSN码: {sn}"

            self.log(f"设备连接成功:\n{device_info_str}")

            # 更新状态栏
            self.device_status.config(text=f"设备状态: 已连接 - {model}")

            # 改变状态栏颜色为绿色表示连接成功
            self.device_status.config(bg="#90EE90")

        except Exception as e:
            self.log(f"设备连接失败: {str(e)}")
            messagebox.showerror("错误", f"连接设备失败: {str(e)}")

            # 更新状态栏为错误状态
            self.device_status.config(text=f"设备状态: 连接失败")
            self.device_status.config(bg="#FFCCCB")

    def get_element(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return None

        method = self.locate_method.get()
        value = self.element_entry.get().strip()

        # 检查是否使用了默认提示文本
        default_placeholders = [
            "请输入文本内容",
            "com.example.app:id/element_id",
            "360,720",
            "//*[@text='文本内容']",
            "android.widget.TextView"
        ]

        if not value or value in default_placeholders:
            self.log("错误: 请输入有效的元素取值")
            return None

        try:
            # 如果是xpath方式，使用xpath方法
            if method == "xpath":
                return self.d.xpath(value)
            # 其他方式使用字典参数
            else:
                return self.d(**{method: value})
        except Exception as e:
            self.log(f"元素定位失败: {str(e)}")
            return None

    def click_element(self):
        # 获取元素取值
        value = self.element_entry.get().strip()
        if not value:
            self.log("错误: 请先输入元素取值")
            return

        # 获取定位方式
        method = self.locate_method.get()

        # 如果是坐标方式，直接点击坐标
        if method == "coordinates":
            try:
                coords = value.split(',')
                if len(coords) == 2:
                    x, y = int(coords[0]), int(coords[1])
                    self.d.click(x, y)
                    self.log(f"点击坐标成功: ({x}, {y})")
                else:
                    self.log("错误: 坐标格式不正确，应为x,y")
            except Exception as e:
                self.log(f"点击坐标失败: {str(e)}")
            return

        # 其他定位方式，使用get_element获取元素
        element = self.get_element()
        if element:
            try:
                element.click()
                self.log(f"点击元素成功: {method}={value}")
            except Exception as e:
                self.log(f"点击失败: {str(e)}")





    def dump_hierarchy(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            # 获取UI树
            xml_string = self.d.dump_hierarchy()

            # 格式化UI树，只显示指定属性并按层级缩进
            formatted_ui = self.format_ui_hierarchy(xml_string)

            # 使用我们已经设置的输出目录（桌面上的"测试导出"文件夹）
            # 生成带时间戳的文件名，避免重复
            timestamp = time.strftime("%Y%m%d_%H%M%S")

            # 保存原始XML和格式化的文本文件
            xml_path = os.path.join(self.output_dir, f"ui_hierarchy_{timestamp}.xml")
            txt_path = os.path.join(self.output_dir, f"ui_hierarchy_{timestamp}.txt")

            # 尝试写入原始XML文件
            with open(xml_path, "w", encoding="utf-8") as f:
                f.write(xml_string)

            # 尝试写入格式化的文本文件
            with open(txt_path, "w", encoding="utf-8") as f:
                f.write(formatted_ui)

            self.log(f"UI树已保存: \n原始XML: {xml_path}\n格式化文本: {txt_path}")
            messagebox.showinfo("UI树", f"UI结构已保存到:\n原始XML: {xml_path}\n格式化文本: {txt_path}")

            # 尝试打开文件所在目录
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(os.path.dirname(txt_path))
                else:  # macOS 或 Linux
                    subprocess.run(['open' if os.name == 'posix' else 'xdg-open', os.path.dirname(txt_path)])
            except:
                pass  # 如果打开目录失败，忽略错误

        except PermissionError:
            self.log("权限错误: 无法写入文件。请确保程序有写入权限。")
            messagebox.showerror("错误", "权限被拒绝，无法保存UI树。\n\n请尝试以管理员身份运行程序或选择其他目录。")
        except Exception as e:
            self.log(f"获取UI树失败: {str(e)}")
            messagebox.showerror("错误", f"获取UI树失败:\n{str(e)}")

    def do_swipe(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            # 获取屏幕尺寸
            width, height = self.d.window_size()

            # 获取滑动方向
            direction = self.swipe_direction.get()

            # 获取滑动时间
            try:
                duration = float(self.swipe_duration.get())
                if duration <= 0:
                    self.log("错误: 滑动时间必须大于0")
                    return
            except ValueError:
                self.log("错误: 滑动时间必须是数字")
                return

            # 根据方向执行滑动
            if direction == "up":
                # 向上滑动（从屏幕下方向上方滑动）
                self.d.swipe(width // 2, height * 0.7, width // 2, height * 0.3, duration)
                self.log(f"向上滑动成功")
            elif direction == "down":
                # 向下滑动（从屏幕上方向下方滑动）
                self.d.swipe(width // 2, height * 0.3, width // 2, height * 0.7, duration)
                self.log(f"向下滑动成功")
            elif direction == "left":
                # 向左滑动（从屏幕右侧向左侧滑动）
                self.d.swipe(width * 0.7, height // 2, width * 0.3, height // 2, duration)
                self.log(f"向左滑动成功")
            elif direction == "right":
                # 向右滑动（从屏幕左侧向右侧滑动）
                self.d.swipe(width * 0.3, height // 2, width * 0.7, height // 2, duration)
                self.log(f"向右滑动成功")
        except Exception as e:
            self.log(f"滑动失败: {str(e)}")



    def press_key(self, key_name, display_name=None):  # 通用按键功能
        if not self.d:
            self.log("错误: 未连接设备")
            return

        if display_name is None:
            display_name = key_name

        try:
            self.d.press(key_name)
            self.log(f"已触发{display_name}键")
        except Exception as e:
            self.log(f"{display_name}键失败: {str(e)}")

    def press_back(self):  # 返回键功能
        self.press_key("back", "返回")

    def press_home(self):  # Home键功能
        self.press_key("home", "Home")

    def press_recent(self):  # 最近任务键功能
        self.press_key("recent", "最近任务")

    def press_power(self):  # 电源键功能
        self.press_key("power", "电源")

    def get_device_sn(self):
        """获取设备SN码和电脑MAC地址的辅助方法。"""
        # 获取SN码
        sn = self.get_device_sn_only()

        # 获取电脑MAC地址
        mac = self.get_computer_mac()

        # 返回组合信息
        return f"{sn} | 电脑MAC: {mac}"

    def get_device_sn_only(self):
        """获取设备SN码的辅助方法。尝试多种方式获取。"""
        # 方式1: 使用uiautomator2的shell命令
        try:
            if self.d:
                sn = self.d.shell('getprop ro.serialno').strip()
                if sn:
                    return sn
        except:
            pass

        # 方式2: 使用另一个属性
        try:
            if self.d:
                sn = self.d.shell('getprop ro.boot.serialno').strip()
                if sn:
                    return sn
        except:
            pass

        # 方式3: 直接使用adb命令
        try:
            result = subprocess.run(['adb', 'get-serialno'], capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip()
        except:
            pass

        # 方式4: 使用adb devices命令
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # 第一行是标题
                    for line in lines[1:]:  # 从第二行开始
                        parts = line.split('\t')
                        if len(parts) >= 1 and parts[0].strip():
                            return parts[0].strip()
        except:
            pass

        return '未知SN码'

    def get_computer_mac(self):
        """获取电脑MAC地址的辅助方法。"""
        try:
            if os.name == 'nt':  # Windows
                mac_cmd = subprocess.run('getmac /v /fo list', capture_output=True, text=True, shell=True)
                mac_output = mac_cmd.stdout
                # 提取第一个物理地址
                mac_match = re.search(r'物理地址:.*?([0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2})', mac_output, re.DOTALL)
                if mac_match:
                    return mac_match.group(1)
                else:
                    # 尝试另一种方式
                    mac_match = re.search(r'([0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2}-[0-9A-F]{2})', mac_output)
                    return mac_match.group(1) if mac_match else '未知MAC'
            else:  # macOS/Linux
                mac_cmd = subprocess.run("ifconfig | grep -E 'ether|HWaddr' | head -n 1", capture_output=True, text=True, shell=True)
                mac_output = mac_cmd.stdout
                mac_match = re.search(r'([0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2})', mac_output)
                return mac_match.group(1) if mac_match else '未知MAC'
        except Exception as e:
            return f'未知MAC ({str(e)[:20]}...)'

        return '未知MAC'

    def format_ui_hierarchy(self, xml_string):
        """将XML格式的UI树转换为格式化的文本，只显示指定属性并按层级缩进。"""
        try:
            # 使用lxml解析XML，它支持getparent()方法
            parser = etree.XMLParser(recover=True)  # recover=True可以处理不规范的XML
            root = etree.fromstring(xml_string.encode('utf-8'), parser)

            # 生成格式化的文本
            formatted_text = []

            def process_element(element, level=0, is_last_child=False, parent_xpath='/hierarchy'):
                # 获取属性
                attrib = element.attrib
                resource_id = attrib.get('resource-id', '无 ID')
                class_name = attrib.get('class', '无类名')
                bounds_str = attrib.get('bounds', '无坐标')
                text = attrib.get('text', '无文本')

                # 处理坐标格式，转换为X和Y值
                try:
                    # 尝试解析坐标格式 [x1,y1][x2,y2]
                    import re
                    coords = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
                    if len(coords) == 2:
                        x1, y1 = int(coords[0][0]), int(coords[0][1])
                        x2, y2 = int(coords[1][0]), int(coords[1][1])
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        bounds = f"X: {center_x}, Y: {center_y}"
                    else:
                        bounds = bounds_str
                except:
                    bounds = bounds_str

                # 生成当前元素的xpath
                current_xpath = parent_xpath + '/' + element.tag

                # 如果有同名兄弟节点，添加索引
                if element.getparent() is not None:
                    siblings = element.getparent().findall(element.tag)
                    if len(siblings) > 1:
                        index = siblings.index(element) + 1
                        current_xpath += f"[{index}]"

                # 生成缩进和标记
                indent = "│   " * level  # 每个级别缩进
                child_marker = "└── " if is_last_child else "├── "

                # 格式化当前元素的信息
                element_info = f"{indent}{child_marker}"
                if text and text.strip():  # 只显示非空文本
                    element_info += f"文本: {text}\n{indent}    "
                else:
                    element_info += f"\n{indent}    "
                element_info += f"资源ID: {resource_id}\n{indent}    "
                element_info += f"坐标: {bounds}\n{indent}    "
                element_info += f"{class_name}\n{indent}    "
                element_info += f"路径: {current_xpath}\n"

                formatted_text.append(element_info)

                # 处理子元素
                children = list(element)
                for i, child in enumerate(children):
                    is_last = (i == len(children) - 1)
                    process_element(child, level + 1, is_last, current_xpath)

            # 处理根元素
            process_element(root)

            return ''.join(formatted_text)
        except Exception as e:
            # 如果lxml失败，尝试使用简化的方法
            try:
                # 使用标准ElementTree库
                root = ET.fromstring(xml_string)

                # 简化的格式化方法，不使用getparent()
                def simple_format(element, level=0, parent_path='/hierarchy'):
                    result = []
                    # 获取属性
                    attrib = element.attrib
                    resource_id = attrib.get('resource-id', '无 ID')
                    class_name = attrib.get('class', '无类名')
                    bounds_str = attrib.get('bounds', '无坐标')
                    text = attrib.get('text', '无文本')

                    # 处理坐标格式，计算中心点
                    try:
                        # 尝试解析坐标格式 [x1,y1][x2,y2]
                        coords = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
                        if len(coords) == 2:
                            x1, y1 = int(coords[0][0]), int(coords[0][1])
                            x2, y2 = int(coords[1][0]), int(coords[1][1])
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            bounds = f"X: {center_x}, Y: {center_y}"
                        else:
                            bounds = bounds_str
                    except:
                        bounds = bounds_str

                    # 生成简化的xpath
                    current_path = f"{parent_path}/{element.tag}"

                    # 缩进和标记
                    indent = "| " * level
                    child_marker = "+-- "

                    # 格式化当前元素的信息
                    result.append(f"{indent}{child_marker}")
                    if text and text.strip():
                        result.append(f"文本: {text}\n{indent}    ")
                    else:
                        result.append(f"\n{indent}    ")
                    result.append(f"资源ID: {resource_id}\n{indent}    ")
                    result.append(f"坐标: {bounds}\n{indent}    ")
                    result.append(f"{class_name}\n{indent}    ")
                    result.append(f"路径: {current_path}\n")

                    # 递归处理子元素
                    for child in element:
                        result.extend(simple_format(child, level + 1, current_path))

                    return result

                return ''.join(simple_format(root))
            except Exception as nested_e:
                return f"解析UI树失败: {str(e)}\n第二次尝试失败: {str(nested_e)}\n\n原始XML片段:\n{xml_string[:300]}..."


    def setup_status_bar(self):
        # 创建状态栏
        self.status_frame = tk.Frame(self.root, bd=1, relief=tk.SUNKEN)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # 设备状态标签
        self.device_status = tk.Label(self.status_frame, text="设备状态: 未连接", bd=1, relief=tk.SUNKEN, anchor=tk.W, font=('Arial', 9))
        self.device_status.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 版本信息
        version_label = tk.Label(self.status_frame, text="v1.2", bd=1, relief=tk.SUNKEN, font=('Arial', 9))
        version_label.pack(side=tk.RIGHT)

    def wait_element(self):
        # 获取元素取值
        value = self.element_entry.get().strip()
        if not value:
            self.log("错误: 请先输入元素取值")
            return

        # 获取定位方式
        method = self.locate_method.get()

        # 获取超时时间
        try:
            timeout = float(self.wait_timeout.get())
        except ValueError:
            self.log("错误: 超时时间必须是数字")
            return

        # 如果是坐标方式，提示不支持
        if method == "coordinates":
            self.log("错误: 坐标方式不支持等待操作")
            return

        # 其他定位方式，使用get_element获取元素
        element = self.get_element()
        if not element:
            return

        try:
            self.log(f"开始等待元素: {method}={value}, 超时{timeout}秒")

            if element.wait(timeout=timeout):
                self.log(f"元素已出现，等待成功")
            else:
                self.log(f"等待超时，元素未出现")
        except Exception as e:
            self.log(f"等待失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = UIAutomatorGUI(root)
    root.mainloop()