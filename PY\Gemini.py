import tkinter as tk  # 导入tkinter库，用于创建GUI。
from tkinter import ttk  # 导入ttk模块，提供更现代的GUI控件。
from tkinter import scrolledtext  # 导入scrolledtext模块，用于创建滚动文本框。
import uiautomator2 as u2  # 导入uiautomator2库，用于自动化Android设备。
import time  # 导入time模块，用于处理时间相关操作。
import random  # 导入random模块，用于生成随机数。
import threading  # 导入threading模块，用于创建和管理线程。
import datetime  # 导入datetime模块，用于获取当前时间。

class AutomationTool:  # 定义AutomationTool类。
    def __init__(self, root):  # 初始化方法。
        self.root = root  # 设置根窗口。
        self.root.title("自动化工具")  # 设置窗口标题。
        self.root.geometry("480x640")  # 设置窗口大小。
        self.root.resizable(False, False)  # 设置窗口不可调整大小。

        # 定义统一字体样式
        self.font_style = ('楷体', 12)  # 设置字体样式

        # 创建主框架
        self.main_frame = tk.Frame(root)  # 创建主框架。
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)  # 放置主框架

        # 创建左右框架
        self.left_frame = tk.Frame(self.main_frame, width=240)  # 创建左框架。
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=2)  # 放置左框架
        self.left_frame.pack_propagate(False)  # 禁止左框架自动调整大小

        self.right_frame = tk.Frame(self.main_frame, width=240)  # 创建右框架。
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=2)  # 放置右框架

        # 创建单号输入区域
        self.input_area = scrolledtext.ScrolledText(  # 创建滚动文本框
            self.left_frame,
            width=25,
            height=25,
            font=self.font_style  # 设置字体样式
        )
        self.input_area.pack(fill=tk.BOTH, expand=True)  # 放置滚动文本框

        # 创建进度条
        self.progress_frame = tk.Frame(self.left_frame)  # 创建进度条框架。
        self.progress_frame.pack(fill=tk.X, pady=5)  # 放置进度条框架

        self.progress = ttk.Progressbar(self.progress_frame, mode='determinate')  # 创建进度条。
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)  # 放置进度条

        # 设置进度条颜色
        style = ttk.Style()  # 创建样式对象。
        style.configure("Horizontal.TProgressbar", background='#88DB29')  # 配置进度条颜色
        self.progress.configure(style="Horizontal.TProgressbar")  # 应用样式

        self.progress_label = tk.Label(self.progress_frame, text="0%", font=self.font_style)  # 创建进度标签。
        self.progress_label.pack(side=tk.LEFT, padx=5)  # 放置进度标签

        # 创建进度输出框
        self.progress_output = scrolledtext.ScrolledText(  # 创建滚动文本框
            self.left_frame,
            height=6,
            font=self.font_style  # 设置字体样式
        )
        self.progress_output.pack(fill=tk.X)  # 放置滚动文本框

        # 初始化变量
        self.buttons = {}  # 初始化按钮字典
        self.active_mode = None  # 初始化当前模式
        self.active_function = None  # 初始化当前功能
        self.running = False  # 初始化运行状态
        self.paused = False  # 初始化暂停状态
        self.current_thread = None  # 初始化当前线程
        self.device = None  # 初始化设备对象
        self.device_connected = False # 初始化设备连接状态
        self.stop_event = threading.Event() # 创建停止事件，用于控制线程终止

        # 创建界面元素
        self.create_buttons()  # 创建按钮
        self.create_speed_control()  # 创建速度控制
        self.create_control_buttons()  # 创建控制按钮
        self.connect_device()  # 连接设备

    def connect_device(self): # 连接设备函数
        try:
            self.device = u2.connect()  # 连接设备
            self.device_connected = True # 更新设备连接状态
            self.log("设备已连接") # 记录日志
        except Exception as e:
            self.log(f"设备连接失败: {e}") # 记录日志
            self.device_connected = False # 更新设备连接状态

    def create_buttons(self):  # 创建按钮方法。
        # 模式按钮
        mode_frame = tk.Frame(self.right_frame)  # 创建模式按钮框架。
        mode_frame.pack(fill=tk.X, pady=10)  # 放置模式按钮框架

        button_style = {  # 定义按钮样式
            'width': 12,
            'height': 2,
            'font': self.font_style,
            'relief': 'raised',
            'bd': 2
        }

        # 创建模式按钮
        mode_buttons = ["顺序模式", "随机模式"]  # 定义模式按钮文本
        for mode in mode_buttons:  # 遍历模式按钮
            btn = tk.Button(mode_frame, text=mode, **button_style)  # 创建按钮
            btn.configure(command=lambda m=mode: self.toggle_button(m))  # 设置按钮命令
            btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置按钮
            self.buttons[mode] = btn  # 将按钮添加到按钮字典

        # 功能按钮，按新的顺序排列
        functions = [  # 定义功能按钮文本
            ("中通揽发", "中通建包"),
            ("兔喜入库", "兔喜出库"),
            ("韵达揽收", "韵达乡镇"),
            ("韵达集包", "韵超入库"),
            ("韵超出库", "多多出库"),
            ("多多入库", None)
        ]

        button_frame = tk.Frame(self.right_frame)  # 创建功能按钮框架。
        button_frame.pack(fill=tk.BOTH, expand=True, pady=10)  # 放置功能按钮框架

        for left_text, right_text in functions:  # 遍历功能按钮
            frame = tk.Frame(button_frame)  # 创建功能按钮子框架。
            frame.pack(fill=tk.X, pady=8)  # 放置功能按钮子框架

            left_btn = tk.Button(frame, text=left_text, **button_style)  # 创建左侧按钮
            left_btn.configure(command=lambda btn=left_text: self.toggle_button(btn))  # 设置左侧按钮命令
            left_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置左侧按钮
            self.buttons[left_text] = left_btn  # 将左侧按钮添加到按钮字典

            if right_text:  # 只有当right_text不为None
                right_btn = tk.Button(frame, text=right_text, **button_style)  # 创建右侧按钮
                right_btn.configure(command=lambda btn=right_text: self.toggle_button(btn))  # 设置右侧按钮命令
                right_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置右侧按钮
                self.buttons[right_text] = right_btn  # 将右侧按钮添加到按钮字典

    def create_speed_control(self):  # 创建速度控制方法。
        speed_frame = tk.Frame(self.right_frame)  # 创建速度控制框架。
        speed_frame.pack(fill=tk.X, pady=10)  # 放置速度控制框架

        tk.Label(  # 创建标签
            speed_frame,
            text="延迟时间范围（秒）",
            font=self.font_style  # 设置字体样式
        ).pack()  # 放置标签

        control_frame = tk.Frame(speed_frame)  # 创建控制框架。
        control_frame.pack(pady=5)  # 放置控制框架

        # 添加最小延迟标签和输入框
        tk.Label(  # 创建最小延迟标签
            control_frame,
            text="最小:",
            font=self.font_style  # 设置字体样式
        ).pack(side=tk.LEFT, padx=2)  # 放置最小延迟标签

        self.speed_min = tk.Spinbox(  # 创建最小延迟输入框
            control_frame,
            from_=0,
            to=60,
            increment=0.1,
            width=6,
            font=self.font_style  # 设置字体样式
        )
        self.speed_min.pack(side=tk.LEFT, padx=2)  # 放置最小延迟输入框

        # 添加最大延迟标签和输入框
        tk.Label(  # 创建最大延迟标签
            control_frame,
            text="最大:",
            font=self.font_style  # 设置字体样式
        ).pack(side=tk.LEFT, padx=2)  # 放置最大延迟标签

        self.speed_max = tk.Spinbox(  # 创建最大延迟输入框
            control_frame,
            from_=0,
            to=60,
            increment=0.1,
            width=6,
            font=self.font_style  # 设置字体样式
        )
        self.speed_max.pack(side=tk.LEFT, padx=2)  # 放置最大延迟输入框

        # 添加说明文字
        tk.Label(  # 创建说明文字标签
            speed_frame,
            text="不设置则使用默认延迟",
            font=('楷体', 10),
            fg='gray'  # 设置字体颜色
        ).pack(pady=(2, 0))  # 放置说明文字标签

    def create_control_buttons(self):  # 创建控制按钮方法。
        control_frame = tk.Frame(self.right_frame)  # 创建控制按钮框架。
        control_frame.pack(fill=tk.X, pady=10)  # 放置控制按钮框架

        control_button_style = {  # 定义控制按钮样式
            'width': 12,
            'height': 2,
            'font': self.font_style,
            'relief': 'raised',
            'bd': 2
        }

        self.start_btn = tk.Button(control_frame, text="启动", bg='lightblue', **control_button_style)  # 创建启动按钮
        self.start_btn.configure(command=self.start_automation)  # 设置启动按钮命令
        self.start_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置启动按钮

        self.pause_btn = tk.Button(control_frame, text="暂停", bg='lightblue', **control_button_style)  # 创建暂停按钮
        self.pause_btn.configure(command=self.pause_automation)  # 设置暂停按钮命令
        self.pause_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置暂停按钮

    def get_button_color(self, button_text):  # 获取按钮颜色方法。
        if button_text in ["顺序模式", "随机模式"]:  # 如果是模式按钮
            return "#00FFFF"  # 返回青色
        elif button_text in ["中通揽发", "中通建包"]:  # 如果是中通按钮
            return "#1E90FF"  # 返回道奇蓝
        elif button_text in ["兔喜入库", "兔喜出库"]:  # 如果是兔喜按钮
            return "#1E90FF"  # 返回道奇蓝
        elif button_text in ["韵达揽收", "韵达乡镇", "韵达集包", "韵超入库", "韵超出库"]:  # 如果是韵达按钮
            return "#FFD700"  # 返回金色
        elif button_text in ["多多入库", "多多出库"]:  # 如果是多多按钮
            return "#FF6B6B"  # 返回红色
        return "SystemButtonFace"  # 返回默认按钮颜色

    def toggle_button(self, button_text):  # 切换按钮状态方法。
        is_mode = button_text in ["顺序模式", "随机模式"]  # 判断是否是模式按钮

        # 韵达乡镇和随机模式互斥
        if button_text == "随机模式" and self.active_function == "韵达乡镇":  # 如果是随机模式且当前功能是韵达乡镇
            self.log("韵达乡镇不支持随机模式！")  # 记录日志
            return
        elif button_text == "韵达乡镇" and self.active_mode == "随机模式":  # 如果是韵达乡镇且当前功能是随机模式
            self.log("韵达乡镇不支持随机模式！")  # 记录日志
            return

        # 如果按钮已激活
        if ((is_mode and self.active_mode == button_text) or
            (not is_mode and self.active_function == button_text)):
            self.buttons[button_text].configure(relief="raised", bg="SystemButtonFace")  # 恢复按钮状态
            if is_mode:  # 如果是模式按钮
                self.active_mode = None  # 清空当前模式
            else:  # 如果是功能按钮
                self.active_function = None  # 清空当前功能
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.pause_btn.configure(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
            return

        for btn_text, btn in self.buttons.items():  # 遍历所有按钮
            # 如果是模式按钮或功能按钮
            if ((is_mode and btn_text in ["顺序模式", "随机模式"]) or
               (not is_mode and btn_text not in ["顺序模式", "随机模式"])):
                btn.configure(relief="raised", bg="SystemButtonFace")  # 恢复按钮状态

        self.buttons[button_text].configure(relief="sunken", bg=self.get_button_color(button_text))  # 设置按钮状态

        if is_mode:  # 如果是模式按钮
            self.active_mode = button_text  # 设置当前模式
            if self.active_function:  # 如果有当前功能
                self.buttons[self.active_function].configure(  # 设置功能按钮状态
                    relief="sunken",
                    bg=self.get_button_color(self.active_function)
                )
        else:  # 如果是功能按钮
            self.active_function = button_text  # 设置当前功能
            if self.active_mode:  # 如果有当前模式
                self.buttons[self.active_mode].configure(  # 设置模式按钮状态
                    relief="sunken",
                    bg=self.get_button_color(self.active_mode)
                )

    def reset_interface(self):  # 重置界面方法。
        # 清空输入框
        self.input_area.delete('1.0', tk.END)  # 清空输入框内容
        # 清空输出框
        self.progress_output.delete('1.0', tk.END)  # 清空输出框内容
        # 进度条归零
        self.progress['value'] = 0  # 重置进度条
        self.progress_label.config(text="0%")  # 重置进度标签
        # 重置按钮状态
        for btn_text, btn in self.buttons.items():  # 遍历所有按钮
            btn.configure(relief="raised", bg="SystemButtonFace")  # 恢复按钮状态
        self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
        self.pause_btn.configure(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
        # 重置状态变量
        self.active_mode = None  # 清空当前模式
        self.active_function = None  # 清空当前功能
        self.running = False  # 停止运行

    def start_automation(self):  # 启动自动化方法
        if not (self.active_mode and self.active_function):  # 如果没有选择模式和功能
            self.log("请先选择模式和功能！")  # 记录日志
            return

        if not self.device_connected:  # 如果设备未连接
            self.log("设备未连接，请检查设备连接！")  # 记录日志
            return

        if self.running: # 如果正在运行
            self.log("自动化已经在运行！") # 记录日志
            return # 直接返回

        self.running = True  # 设置运行状态为 True
        self.stop_event.clear()  # 清除停止事件标志

        self.current_thread = threading.Thread(target=self.run_selected_task) # 创建线程
        self.current_thread.daemon = True  # 设置为守护线程，主线程退出时自动退出
        self.current_thread.start()  # 启动线程
        self.start_btn.config(relief="sunken", bg="#9370DB") # 设置启动按钮样式

    def pause_automation(self):  # 暂停自动化方法
        if self.running and not self.paused:  # 如果正在运行且未暂停
            self.paused = True  # 设置暂停状态为 True
            self.log("自动化已暂停") # 记录日志
            self.pause_btn.config(relief="sunken", bg="#9370DB") # 设置暂停按钮样式
        elif self.running and self.paused: # 如果正在运行且已暂停
             self.paused = False # 设置暂停状态为 False
             self.log("自动化已继续") # 记录日志
             self.pause_btn.config(relief="raised", bg="lightblue") # 设置暂停按钮样式
        else: # 如果没有运行
            self.log("自动化未运行，无法暂停") # 记录日志

    def highlight_line(self, number):  # 高亮显示行方法。
        content = self.input_area.get('1.0', tk.END)  # 获取文本框内容
        lines = content.splitlines()  # 按行分割内容
        for i, line in enumerate(lines):  # 遍历每一行
            if line.strip() == number:  # 如果找到匹配的单号
                start = f"{i + 1}.0"  # 设置起始位置
                end = f"{i + 1}.end"  # 设置结束位置
                self.input_area.tag_add("highlight", start, end)  # 添加高亮标签
                self.input_area.tag_config("highlight", background="#88DB29")  # 设置高亮颜色
                self.input_area.see(start)  # 滚动到高亮行
                break

    def get_delay_range(self):  # 获取延迟范围方法。
        try:
            min_delay = float(self.speed_min.get())  # 获取最小延迟
            max_delay = float(self.speed_max.get())  # 获取最大延迟
            if min_delay == 0 and max_delay == 0:  # 如果未设置延迟
                return None
            return (min_delay, max_delay)  # 返回延迟范围
        except ValueError:  # 如果输入无效
            return None

    def log(self, message):  # 记录日志方法。
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 获取当前时间戳
        self.progress_output.insert(tk.END, f"{timestamp} - {message}\n")  # 插入日志信息
        self.progress_output.see(tk.END)  # 滚动到最新日志

    def get_numbers_from_input(self):  # 从输入框获取单号列表的函数
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取文本框内容并按行分割
        return [num.strip() for num in numbers if num.strip()]  # 移除空行和空格

    def update_progress(self, current, total):  # 更新进度条的函数
        progress = (current) / total * 100  # 计算进度百分比
        self.progress['value'] = progress  # 更新进度条的值
        self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签的文本

    def get_random_delay(self):  # 获取随机延迟时间的函数
        delay_range = self.get_delay_range()  # 获取延迟范围
        if delay_range:  # 如果设置了延迟范围
            return random.uniform(delay_range[0], delay_range[1])  # 返回该范围内的随机数
        return None  # 否则返回None

    def handle_device_disconnect(self):  # 处理设备断开连接的函数
        self.log("设备已断开连接！")  # 记录日志
        self.device_connected = False  # 设置设备连接状态为False
        self.running = False  # 停止运行
        self.start_btn.config(relief="raised", bg="lightblue")  # 恢复启动按钮状态
        self.pause_btn.config(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
        self.stop_event.set() # 设置停止事件

    def run_selected_task(self):  # 运行选定任务的函数
        try:
            if self.active_function == "多多入库":  # 如果选择的是多多入库
                self.run_task(self.duoduo_inbound_task)  # 运行多多入库任务
            elif self.active_function == "多多出库":  # 如果选择的是多多出库
                self.run_task(self.duoduo_outbound_task)  # 运行多多出库任务
            elif self.active_function == "中通建包":  # 如果选择的是中通建包
                self.run_task(self.zhongtong_build_task)  # 运行中通建包任务
            elif self.active_function == "韵达集包":  # 如果选择的是韵达集包
                self.run_task(self.yunda_build_task)  # 运行韵达集包任务
            elif self.active_function == "韵达乡镇":  # 如果选择的是韵达乡镇
                self.run_task(self.yunda_township_task)  # 运行韵达乡镇任务
            elif self.active_function == "中通揽发":  # 如果选择的是中通揽发
                self.run_task(self.zhongtong_pickup_task)  # 运行中通揽发任务
            elif self.active_function == "韵达揽收":  # 如果选择的是韵达揽收
                self.run_task(self.yunda_pickup_task)  # 运行韵达揽收任务
            elif self.active_function == "兔喜入库":  # 如果选择的是兔喜入库
                self.run_task(self.tuxi_inbound_task)  # 运行兔喜入库任务
        except u2.exceptions.DeviceConnectionError as e:  # 捕获设备连接错误
            self.handle_device_disconnect()  # 调用设备断开连接处理函数
        except Exception as e:  # 捕获其他异常
            self.log(f"发生错误: {e}")  # 记录错误日志
        finally:  # 最终执行
            self.running = False  # 设置运行状态为False
            self.start_btn.config(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.stop_event.set()  # 设置停止事件

    def pause_automation(self): #暂停自动化
        if self.current_thread and self.running: # 如果线程存在且正在运行
            self.paused = not self.paused # 切换暂停状态
            if self.paused: # 如果暂停
                self.log("自动化已暂停") # 记录日志
            else: # 如果继续
                self.log("自动化已继续") # 记录日志

    def stop_automation(self): #停止自动化
        self.running = False # 设置运行状态为False
        self.stop_event.set() # 设置停止事件
        self.log("正在停止自动化...") # 记录日志
        if self.current_thread: # 如果线程存在
            self.current_thread.join() # 等待线程结束
        self.log("自动化已停止") # 记录日志

    def duoduo_inbound_task(self, device, num, automation_tool):  # 多多入库的任务特定函数
        try:
            device(resourceId="com.xunmeng.station:id/et_with_delete", text="运单号").set_text(num)  # 输入运单号
            time.sleep(0.1) # 等待输入完成
            device.press("back")  # 按下返回键

            device(resourceId="com.xunmeng.station:id/et_with_delete", text="手机号").set_text("13800138000")  # 输入手机号
            automation_tool.log("输入固定号码")  # 记录日志
            time.sleep(0.1) # 等待输入完成
            device.press("back")  # 按下返回键

            device(resourceId="com.xunmeng.station:id/tv_scan_confirm").click()  # 点击确认按钮
        except u2.exceptions.UiObjectNotFoundError as e:  # 捕获异常
            automation_tool.log(f"多多入库 - UI元素未找到: {e}") # 记录日志
            raise  # 重新抛出异常，让run_task处理
        except Exception as e: #捕获其他异常
            automation_tool.log(f"多多入库出错: {e}") #记录日志
            raise # 重新抛出异常，让run_task处理

    def duoduo_outbound_task(self, device, num, automation_tool):  # 多多出库的任务特定函数
        try:
            device(resourceId="com.xunmeng.station:id/et_with_delete").set_text(num)  # 输入单号
            #device.click(606, 1196)  # 点击屏幕坐标 - 避免使用固定坐标
            confirm_button = device(resourceId="com.xunmeng.station:id/tv_confirm_pop_v2")  # 查找确认按钮
            if confirm_button.exists(timeout=5): # 如果找到确认按钮
                confirm_button.click() # 点击确认按钮
                automation_tool.log("出库成功") # 记录日志
            else: # 如果没找到
                automation_tool.log("未找到出库确认按钮") # 记录日志

        except u2.exceptions.UiObjectNotFoundError as e:
            automation_tool.log(f"多多出库 - UI元素未找到: {e}")
            raise
        except Exception as e:
            automation_tool.log(f"多多出库出错: {e}")
            raise

    def zhongtong_build_task(self, device, num, automation_tool):  # 中通建包的任务特定函数
        try:
            device(text="请输入单号或包号或环保袋").set_text(num)  # 输入单号
            device(resourceId="com.zto.pdaunity:id/btn_done").click()  # 点击完成按钮
        except u2.exceptions.UiObjectNotFoundError as e:
            automation_tool.log(f"中通建包 - UI元素未找到: {e}")
            raise
        except Exception as e:
            automation_tool.log(f"中通建包出错: {e}")
            raise

    def yunda_build_task(self, device, num, automation_tool):  # 韵达集包的任务特定函数
        try:
            # 先处理弹窗（最多等待0.05秒）
            if device(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                device(resourceId="android:id/button1").click()  # 点击确定按钮
                automation_tool.log("已确定")  # 记录日志

            # 输入单号
            device(resourceId="com.yd.expresswebsite:id/took_shipment_collection_bill_number").set_text(num)  # 输入单号
            device(text="添加").click()  # 点击添加按钮

        except u2.exceptions.UiObjectNotFoundError as e:
            automation_tool.log(f"韵达集包 - UI元素未找到: {e}")
            raise
        except Exception as e:
            automation_tool.log(f"韵达集包出错: {e}")
            raise

    def yunda_township_task(self, device, num, automation_tool):  # 韵达乡镇的任务特定函数
        try:
            # 先处理弹窗（最多等待0.1秒）
            if device(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                device(resourceId="android:id/button1").click()  # 点击确定按钮
                automation_tool.log("已确定")  # 记录日志

            if device(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                device(resourceId="android:id/button1").click()  # 点击确定按钮
                automation_tool.log("已确定")  # 记录日志

            if len(num) < 6:  # 如果单号长度小于6
                # 输入到站点编码输入框
                device(resourceId="com.yd.expresswebsite:id/shipment_business_shipment_give_shipment_station_code").set_text(num)  # 输入站点编码
                # 输入站点编码后处理弹窗
                if device(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                    device(resourceId="android:id/button1").click()  # 点击确定按钮
                    automation_tool.log("已确定")  # 记录日志
            else:  # 如果单号长度大于等于6
                # 输入到单号输入框
                device(resourceId="com.yd.expresswebsite:id/shipment_business_shipment_give_shipment_bill_number").set_text(num)  # 输入单号

            # 点击添加按钮
            device(text="添加").click()  # 点击添加按钮

        except u2.exceptions.UiObjectNotFoundError as e:
            automation_tool.log(f"韵达乡镇 - UI元素未找到: {e}")
            raise
        except Exception as e:
            automation_tool.log(f"韵达乡镇出错: {e}")
            raise

    def zhongtong_pickup_task(self, device, num, automation_tool):  # 中通揽发的任务特定函数
        # 中通揽发功能待实现
        automation_tool.log("中通揽发功能待实现")  # 记录日志
        pass  # 空语句

    def yunda_pickup_task(self, device, num, automation_tool):  # 韵达揽收的任务特定函数
        # 韵达揽收功能待实现
        automation_tool.log("韵达揽收功能待实现")  # 记录日志
        pass  # 空语句

    def click_pickup_if_exists(self, device, timeout=0.1):  # 点击"自提"按钮的辅助函数
        try:
            if device(resourceId="com.zto.families.ztofamilies:id/tv_three_two").wait(timeout=timeout):  # 使用显式等待
                device(resourceId="com.zto.families.ztofamilies:id/tv_three_two").click()  # 点击"自提"按钮
                self.log("自提确认")  # 记录日志
                return True  # 返回True表示点击成功
        except Exception:
            pass  # 忽略异常，继续执行
        return False  # 返回False表示未找到或点击失败

    def click_continue_if_exists(self, device, timeout=0.1):  # 点击"继续入库"按钮的辅助函数
        try:
            if device(resourceId="com.zto.families.ztofamilies:id/tv_cancel").wait(timeout=timeout):  # 使用显式等待
                device(resourceId="com.zto.families.ztofamilies:id/tv_cancel").click()  # 点击"继续入库"按钮
                self.log("继续入库")  # 记录日志
                return True  # 返回True表示点击成功
        except Exception:
            pass  # 忽略异常，继续执行
        return False  # 返回False表示未找到或点击失败

    def click_delivery_if_exists(self, device, timeout=0.1):  # 点击"送货上门"按钮的辅助函数
        try:
            if device(resourceId="com.zto.families.ztofamilies:id/tv_sure").wait(timeout=timeout):  # 使用显式等待
                device(resourceId="com.zto.families.ztofamilies:id/tv_sure").click()  # 点击"送货上门"按钮
                self.log("送货上门")  # 记录日志
                return True  # 返回True表示点击成功
        except Exception:
            pass  # 忽略异常，继续执行
        return False  # 返回False表示未找到或点击失败

    def tuxi_inbound_task(self, device, num, automation_tool):  # 兔喜入库的任务特定函数
        try:
            # 将单号输入到指定的输入框
            if device(resourceId="com.zto.families.ztofamilies:id/edit_storage_wb").wait(timeout=2): #等待输入框出现
                 device(resourceId="com.zto.families.ztofamilies:id/edit_storage_wb").set_text(num) # 找到输入框输入单号

            # 等待并输入固定手机号码,并判断是否存在
            if device(resourceId="com.zto.families.ztofamilies:id/edit_phone_num").wait(timeout=2):
                device(resourceId="com.zto.families.ztofamilies:id/edit_phone_num").set_text("13800138000")
                automation_tool.log("输入固定号码") # 记录日志

            self.click_pickup_if_exists(device)  # 点击自提，如果存在
            self.click_continue_if_exists(device)  # 点击继续入库，如果存在
            self.click_delivery_if_exists(device)  # 点击送货上门，如果存在


        except u2.exceptions.UiObjectNotFoundError as e:  # 捕获异常
            automation_tool.log(f"兔喜入库 - UI元素未找到: {e}")  # 记录日志
            raise  # 重新抛出异常，让run_task处理
        except Exception as e:  # 捕获其他异常
            automation_tool.log(f"兔喜入库出错: {e}")  # 记录日志
            raise  # 重新抛出异常，让run_task处理

    def run_task(self, task_function):  # 运行任务的通用函数
        numbers = self.get_numbers_from_input()  # 获取单号列表
        if not numbers:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return  # 退出函数

        if self.active_mode == "随机模式":  # 如果是随机模式
            random.shuffle(numbers)  # 随机打乱单号顺序

        if not self.device_connected:  # 如果设备未连接
            self.log("设备未连接，无法开始任务。")  # 记录日志
            return  # 退出函数

        try:
            self.device.set_input_ime(True)  # 启用输入法
            total_numbers = len(numbers)
            for index, num in enumerate(numbers):  # 遍历单号
                if self.stop_event.is_set():  # 检查是否设置了停止事件
                    self.log("任务被终止。")
                    break  # 停止循环

                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待

                self.update_progress(index + 1, total_numbers)  # 更新进度条
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total_numbers}, 进度: {index + 1}/{total_numbers}, 当前单号: {num}")  # 记录日志

                try:
                    task_function(self.device, num, self)  # 调用任务特定的函数，传递设备对象、单号和self
                except u2.exceptions.UiObjectNotFoundError as e:
                    self.log(f"UI元素未找到: {e}")
                    continue  # 继续下一个单号
                except Exception as e:  # 捕获异常
                    self.log(f"单号 {num} 处理出错: {e}")  # 记录日志
                    continue  # 继续下一个单号

                delay = self.get_random_delay()  # 获取随机延迟
                if delay:  # 如果有延迟
                    time.sleep(delay)  # 延迟
                else:  # 否则
                    time.sleep(0.1)  # 默认延迟

        except u2.exceptions.DeviceConnectionError as e:  # 设备连接错误
            self.handle_device_disconnect()  # 处理设备断开连接
        except Exception as e:  # 其他错误
            self.log(f"发生错误: {e}")  # 记录日志
        finally:  # 最后执行
            if self.device_connected:
                self.device.set_input_ime(False)  # 禁用输入法
            self.running = False  # 停止运行
            self.start_btn.config(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.stop_event.set()  # 设置停止事件

    def run_selected_task(self):  # 运行选定任务的函数
        try:
            if self.active_function == "多多入库":  # 如果选择的是多多入库
                self.run_task(self.duoduo_inbound_task)  # 运行多多入库任务
            elif self.active_function == "多多出库":  # 如果选择的是多多出库
                self.run_task(self.duoduo_outbound_task)  # 运行多多出库任务
            elif self.active_function == "中通建包":  # 如果选择的是中通建包
                self.run_task(self.zhongtong_build_task)  # 运行中通建包任务
            elif self.active_function == "韵达集包":  # 如果选择的是韵达集包
                self.run_task(self.yunda_build_task)  # 运行韵达集包任务
            elif self.active_function == "韵达乡镇":  # 如果选择的是韵达乡镇
                self.run_task(self.yunda_township_task)  # 运行韵达乡镇任务
            elif self.active_function == "中通揽发":  # 如果选择的是中通揽发
                self.run_task(self.zhongtong_pickup_task)  # 运行中通揽发任务
            elif self.active_function == "韵达揽收":  # 如果选择的是韵达揽收
                self.run_task(self.yunda_pickup_task)  # 运行韵达揽收任务
            elif self.active_function == "兔喜入库":  # 如果选择的是兔喜入库
                self.run_task(self.tuxi_inbound_task)  # 运行兔喜入库任务
        except u2.exceptions.DeviceConnectionError as e:  # 捕获设备连接错误
            self.handle_device_disconnect()  # 调用设备断开连接处理函数
        except Exception as e:  # 捕获其他异常
            self.log(f"发生错误: {e}")  # 记录错误日志
        finally:  # 最终执行
            self.running = False  # 设置运行状态为False
            self.start_btn.config(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.stop_event.set()  # 设置停止事件

    def pause_automation(self): #暂停自动化
        if self.current_thread and self.running: # 如果线程存在且正在运行
            self.paused = not self.paused # 切换暂停状态
            if self.paused: # 如果暂停
                self.log("自动化已暂停") # 记录日志
            else: # 如果继续
                self.log("自动化已继续") # 记录日志

    def stop_automation(self): #停止自动化
        self.running = False # 设置运行状态为False
        self.stop_event.set() # 设置停止事件
        self.log("正在停止自动化...") # 记录日志
        if self.current_thread: # 如果线程存在
            self.current_thread.join() # 等待线程结束
            self.log("自动化已停止") # 记录日志

if __name__ == "__main__":  # 主程序入口。
    root = tk.Tk()  # 创建根窗口。
    app = AutomationTool(root)  # 创建AutomationTool实例。
    root.protocol("WM_DELETE_WINDOW", app.stop_automation) # 窗口关闭时调用stop_automation
    root.mainloop()  # 进入主事件循环。