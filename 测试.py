import uiautomator2 as u2
import tkinter as tk
from tkinter import messagebox, ttk
import time
import os

class UIAutomatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("uiautomator2 自动化工具")
        self.d = None
        self.screenshot_path = "screenshot.png"

        # 设备连接部分
        self.setup_connection_frame()
        
        # 元素定位部分
        self.setup_element_frame()
        
        # 滑动操作部分
        self.setup_swipe_frame()
        
        # 系统按键部分（新增）
        self.setup_key_frame()
        
        # 操作按钮部分
        self.setup_action_frame()
        
        # 日志输出部分
        self.setup_log_frame()

    def setup_connection_frame(self):
        frame = tk.LabelFrame(self.root, text="设备连接", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        tk.Label(frame, text="设备ID/IP:").grid(row=0, column=0)
        self.device_entry = tk.Entry(frame, width=30)
        self.device_entry.grid(row=0, column=1)
        self.device_entry.insert(0, "自动连接")

        tk.Button(frame, text="连接设备", command=self.connect_device).grid(row=0, column=2, padx=5)

    def setup_element_frame(self):
        frame = tk.LabelFrame(self.root, text="元素定位", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        self.locate_method = tk.StringVar(value="resourceId")
        methods = ["resourceId", "text", "className", "description"]
        for i, method in enumerate(methods):
            tk.Radiobutton(frame, text=method, variable=self.locate_method, value=method).grid(row=0, column=i)

        tk.Label(frame, text="元素值:").grid(row=1, column=0)
        self.element_entry = tk.Entry(frame, width=40)
        self.element_entry.grid(row=1, column=1, columnspan=3, sticky=tk.EW)
        self.element_entry.insert(0, "com.xunmeng.station:id/tv_top_number")

    def setup_swipe_frame(self):
        frame = tk.LabelFrame(self.root, text="滑动操作", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        # 滑动方向选择
        self.swipe_direction = tk.StringVar(value="down")
        tk.Radiobutton(frame, text="向下滑动", variable=self.swipe_direction, value="down").grid(row=0, column=0)
        tk.Radiobutton(frame, text="向上滑动", variable=self.swipe_direction, value="up").grid(row=0, column=1)
        tk.Radiobutton(frame, text="向左滑动", variable=self.swipe_direction, value="left").grid(row=0, column=2)
        tk.Radiobutton(frame, text="向右滑动", variable=self.swipe_direction, value="right").grid(row=0, column=3)

        # 滑动参数
        tk.Label(frame, text="滑动时间(秒):").grid(row=1, column=0)
        self.swipe_duration = tk.Entry(frame, width=5)
        self.swipe_duration.grid(row=1, column=1)
        self.swipe_duration.insert(0, "1.0")

        tk.Button(frame, text="执行滑动", command=self.do_swipe).grid(row=1, column=2, padx=5)
        tk.Button(frame, text="滚动到底部", command=self.scroll_to_end).grid(row=1, column=3, padx=5)

    def setup_key_frame(self):  # 新增系统按键区域
        frame = tk.LabelFrame(self.root, text="系统按键", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        tk.Button(frame, text="返回键", command=self.press_back).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="Home键", command=self.press_home).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="最近任务", command=self.press_recent).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="电源键", command=self.press_power).pack(side=tk.LEFT, padx=5)

    def setup_action_frame(self):
        frame = tk.LabelFrame(self.root, text="操作", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.X)

        tk.Button(frame, text="点击元素", command=self.click_element).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="高亮元素", command=self.highlight_element).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="截图", command=self.take_screenshot).pack(side=tk.LEFT, padx=5)
        tk.Button(frame, text="查看UI树", command=self.dump_hierarchy).pack(side=tk.LEFT, padx=5)

    def setup_log_frame(self):
        frame = tk.LabelFrame(self.root, text="日志输出", padx=10, pady=10)
        frame.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(frame, height=10, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        scrollbar = tk.Scrollbar(frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

    def log(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.config(state=tk.DISABLED)
        self.log_text.see(tk.END)

    def connect_device(self):
        device_id = self.device_entry.get().strip()
        try:
            self.d = u2.connect(device_id if device_id != "自动连接" else None)
            self.log(f"设备连接成功: {self.d.info}")
        except Exception as e:
            self.log(f"设备连接失败: {str(e)}")
            messagebox.showerror("错误", f"连接设备失败: {str(e)}")

    def get_element(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return None

        method = self.locate_method.get()
        value = self.element_entry.get().strip()
        
        if not value:
            self.log("错误: 元素值不能为空")
            return None

        try:
            return self.d(**{method: value})
        except Exception as e:
            self.log(f"元素定位失败: {str(e)}")
            return None

    def click_element(self):
        element = self.get_element()
        if element:
            try:
                element.click()
                self.log(f"点击元素成功: {self.locate_method.get()}={self.element_entry.get()}")
            except Exception as e:
                self.log(f"点击失败: {str(e)}")

    def highlight_element(self):
        element = self.get_element()
        if element:
            try:
                element.highlight(2)
                self.log(f"高亮元素成功")
            except Exception as e:
                self.log(f"高亮失败: {str(e)}")

    def take_screenshot(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            self.d.screenshot(self.screenshot_path)
            self.log(f"截图已保存: {os.path.abspath(self.screenshot_path)}")
            messagebox.showinfo("截图", f"截图已保存到:\n{self.screenshot_path}")
        except Exception as e:
            self.log(f"截图失败: {str(e)}")

    def dump_hierarchy(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            xml = self.d.dump_hierarchy()
            save_path = "ui_hierarchy.xml"
            with open(save_path, "w", encoding="utf-8") as f:
                f.write(xml)
            self.log(f"UI树已保存: {os.path.abspath(save_path)}")
            messagebox.showinfo("UI树", f"UI结构已保存到:\n{save_path}")
        except Exception as e:
            self.log(f"获取UI树失败: {str(e)}")

    def do_swipe(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            direction = self.swipe_direction.get()
            duration = float(self.swipe_duration.get())

            width, height = self.d.window_size()
            x1, y1 = width // 2, height // 2

            if direction == "down":
                x2, y2 = x1, y1 - height // 3
            elif direction == "up":
                x2, y2 = x1, y1 + height // 3
            elif direction == "left":
                x2, y2 = x1 + width // 3, y1
            else:  # right
                x2, y2 = x1 - width // 3, y1

            self.d.swipe(x1, y1, x2, y2, duration)
            self.log(f"滑动成功: 方向={direction}, 时间={duration}秒")
        except Exception as e:
            self.log(f"滑动失败: {str(e)}")

    def scroll_to_end(self):
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            width, height = self.d.window_size()
            for _ in range(3):  # 默认滑动3次确保到底部
                self.d.swipe(width // 2, height * 0.7, width // 2, height * 0.3, 0.5)
                time.sleep(0.5)
            self.log("已滚动到底部")
        except Exception as e:
            self.log(f"滚动失败: {str(e)}")

    def press_back(self):  # 新增返回键功能
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            self.d.press("back")
            self.log("已触发返回键")
        except Exception as e:
            self.log(f"返回键失败: {str(e)}")

    def press_home(self):  # 新增Home键功能
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            self.d.press("home")
            self.log("已触发Home键")
        except Exception as e:
            self.log(f"Home键失败: {str(e)}")

    def press_recent(self):  # 新增最近任务键功能
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            self.d.press("recent")
            self.log("已触发最近任务键")
        except Exception as e:
            self.log(f"最近任务键失败: {str(e)}")

    def press_power(self):  # 新增电源键功能
        if not self.d:
            self.log("错误: 未连接设备")
            return

        try:
            self.d.press("power")
            self.log("已触发电源键")
        except Exception as e:
            self.log(f"电源键失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = UIAutomatorGUI(root)
    root.mainloop()