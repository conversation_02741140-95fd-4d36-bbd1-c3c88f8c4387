import tkinter as tk  # 导入tkinter库
from tkinter import ttk  # 导入ttk模块
from tkinter import scrolledtext  # 导入scrolledtext模块
from tkinter import messagebox  # 导入messagebox模块
import time  # 导入time模块
import random  # 导入random模块
import threading  # 导入threading模块
import win32gui  # 导入win32gui模块
import win32api  # 导入win32api模块
import win32con  # 导入win32con模块
import pyautogui  # 导入pyautogui模块

class AutomationTool:  # 定义AutomationTool类
    def __init__(self, root):  # 初始化方法
        self.root = root  # 设置根窗口
        self.root.title("自动化工具")  # 设置窗口标题
        self.root.geometry("480x640")  # 设置窗口大小
        self.root.resizable(False, False)  # 设置窗口不可调整大小

        # 初始化拖拽相关变量
        self.drag_data = {
            'x': 0,
            'y': 0,
            'dragging': False,
            'original_position': None,
            'overlay': None,
            'target_hwnd': None,
            'web_mode': False,  # 添加网页模式标记
            'web_position': None  # 添加网页元素位置
        }

        # 定义统一字体样式
        self.font_style = ('楷体', 12)  # 设置字体样式

        # 创建主框架
        self.main_frame = tk.Frame(root)  # 创建主框架
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)  # 放置主框架

        # 创建左右框架
        self.left_frame = tk.Frame(self.main_frame, width=240)  # 创建左框架
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=2)  # 放置左框架
        self.left_frame.pack_propagate(False)  # 禁止左框架自动调整大小

        self.right_frame = tk.Frame(self.main_frame, width=240)  # 创建右框架
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=2)  # 放置右框架

        # 创建单号输入区域
        self.input_area = scrolledtext.ScrolledText(  # 创建滚动文本框
            self.left_frame,
            width=25,
            height=25,
            font=self.font_style  # 设置字体样式
        )
        self.input_area.pack(fill=tk.BOTH, expand=True)  # 放置滚动文本框

        # 创建进度条
        self.progress_frame = tk.Frame(self.left_frame)  # 创建进度条框架
        self.progress_frame.pack(fill=tk.X, pady=5)  # 放置进度条框架

        self.progress = ttk.Progressbar(self.progress_frame, mode='determinate')  # 创建进度条
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)  # 放置进度条

        self.progress_label = tk.Label(self.progress_frame, text="0%", font=self.font_style)  # 创建进度标签
        self.progress_label.pack(side=tk.LEFT, padx=5)  # 放置进度标签

        # 创建进度输出框
        self.progress_output = scrolledtext.ScrolledText(  # 创建滚动文本框
            self.left_frame,
            height=6,
            font=self.font_style  # 设置字体样式
        )
        self.progress_output.pack(fill=tk.X)  # 放置滚动文本框

        # 初始化变量
        self.buttons = {}  # 初始化按钮字典
        self.active_mode = None  # 初始化当前模式
        self.active_function = None  # 初始化当前功能
        self.running = False  # 初始化运行状态
        self.paused = False  # 初始化暂停状态
        self.current_thread = None  # 初始化当前线程

        # 创建界面元素
        self.create_buttons()  # 创建按钮
        self.create_speed_control()  # 创建速度控制
        self.create_control_buttons()  # 创建控制按钮

    def create_buttons(self):  # 创建按钮方法
        # 模式按钮
        mode_frame = tk.Frame(self.right_frame)  # 创建模式按钮框架
        mode_frame.pack(fill=tk.X, pady=10)  # 放置模式按钮框架

        button_style = {  # 定义按钮样式
            'width': 12,
            'height': 2,
            'font': self.font_style,
            'relief': 'raised',
            'bd': 2
        }

        # 创建模式按钮
        mode_buttons = ["程序模式", "网页模式"]  # 定义模式按钮文本
        for mode in mode_buttons:  # 遍历模式按钮
            btn = tk.Button(mode_frame, text=mode, **button_style)  # 创建按钮
            btn.configure(command=lambda m=mode: self.toggle_button(m))  # 设置按钮命令
            btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置按钮
            self.buttons[mode] = btn  # 将按钮添加到按钮字典

        # 只保留窗口确定按钮
        button_frame = tk.Frame(self.right_frame)  # 创建功能按钮框架
        button_frame.pack(fill=tk.BOTH, expand=True, pady=10)  # 放置功能按钮框架

        # 创建一个居中的框架
        center_frame = tk.Frame(button_frame)  # 创建居中框架
        center_frame.pack(pady=8)  # 放置居中框架

        # 修改按钮样式，减半宽度
        button_style['width'] = 6  # 减半宽度

        # 创建圆形靶心按钮
        canvas_size = 50  # 画布大小
        btn_frame = tk.Frame(center_frame, width=canvas_size, height=canvas_size)  # 创建按钮框架
        btn_frame.pack(padx=5)  # 放置按钮框架

        # 创建画布
        canvas = tk.Canvas(btn_frame, width=canvas_size, height=canvas_size,
                          highlightthickness=0, bg=self.root.cget('bg'))  # 创建画布
        canvas.pack()  # 放置画布

        # 绘制更美观的靶心图案
        # 外圈 - 红色
        canvas.create_oval(2, 2, canvas_size-2, canvas_size-2, fill='#FF4757', width=0)
        # 中圈 - 白色
        canvas.create_oval(10, 10, canvas_size-10, canvas_size-10, fill='white', width=0)
        # 内圈 - 红色
        canvas.create_oval(18, 18, canvas_size-18, canvas_size-18, fill='#FF4757', width=0)
        # 最内圈 - 白色
        canvas.create_oval(26, 26, canvas_size-26, canvas_size-26, fill='white', width=0)
        # 中心点 - 红色
        canvas.create_oval(canvas_size/2-4, canvas_size/2-4, canvas_size/2+4, canvas_size/2+4, fill='#FF4757', width=0)
        # 添加十字线
        canvas.create_line(canvas_size/2, 5, canvas_size/2, canvas_size-5, fill='#FF4757', width=1)
        canvas.create_line(5, canvas_size/2, canvas_size-5, canvas_size/2, fill='#FF4757', width=1)

        # 将画布作为按钮
        canvas.bind('<Button-1>', self.start_drag)
        canvas.bind('<B1-Motion>', self.on_drag)
        canvas.bind('<ButtonRelease-1>', self.stop_drag)
        canvas.bind('<Enter>', lambda e: canvas.config(cursor="crosshair"))
        canvas.bind('<Leave>', lambda e: canvas.config(cursor=""))

        # 保存到按钮字典
        self.buttons["窗口确定"] = canvas  # 将按钮添加到按钮字典

        # 拖拽事件和鼠标悬停已在上面添加

    def create_speed_control(self):  # 创建速度控制方法
        speed_frame = tk.Frame(self.right_frame)  # 创建速度控制框架
        speed_frame.pack(fill=tk.X, pady=10)  # 放置速度控制框架

        tk.Label(  # 创建标签
            speed_frame,
            text="延迟时间（秒）",
            font=self.font_style  # 设置字体样式
        ).pack()  # 放置标签

        control_frame = tk.Frame(speed_frame)  # 创建控制框架
        control_frame.pack(pady=5)  # 放置控制框架

        # 添加延迟输入框
        self.speed_delay = tk.Spinbox(  # 创建延迟输入框
            control_frame,
            from_=0,
            to=60,
            increment=0.1,
            width=10,
            font=self.font_style  # 设置字体样式
        )
        self.speed_delay.delete(0, tk.END)  # 清除默认值
        self.speed_delay.insert(0, "0.5")  # 设置默认值为0.5秒
        self.speed_delay.pack(padx=2)  # 放置延迟输入框

        # 添加说明文字
        tk.Label(  # 创建说明文字标签
            speed_frame,
            text="输入单号间隔时间（秒）",
            font=('楷体', 10),
            fg='gray'  # 设置字体颜色
        ).pack(pady=(2, 0))  # 放置说明文字标签

    def create_control_buttons(self):  # 创建控制按钮方法
        control_frame = tk.Frame(self.right_frame)  # 创建控制按钮框架
        control_frame.pack(fill=tk.X, pady=10)  # 放置控制按钮框架

        control_button_style = {  # 定义控制按钮样式
            'width': 12,
            'height': 2,
            'font': self.font_style,
            'relief': 'raised',
            'bd': 2
        }

        self.start_btn = tk.Button(control_frame, text="启动", bg='lightblue', **control_button_style)  # 创建启动按钮
        self.start_btn.configure(command=lambda: self.toggle_control_button(self.start_btn))  # 设置启动按钮命令
        self.start_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置启动按钮

        self.pause_btn = tk.Button(control_frame, text="暂停", bg='lightblue', **control_button_style)  # 创建暂停按钮
        self.pause_btn.configure(command=lambda: self.toggle_control_button(self.pause_btn))  # 设置暂停按钮命令
        self.pause_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置暂停按钮

    def get_button_color(self, button_text):  # 获取按钮颜色方法
        if button_text in ["程序模式", "网页模式"]:  # 如果是模式按钮
            return "#00FFFF"  # 返回青色
        elif button_text == "窗口确定":  # 如果是窗口确定按钮
            return "#FF6B6B"  # 返回红色
        return "SystemButtonFace"  # 返回默认按钮颜色

    def toggle_button(self, button_text):  # 切换按钮状态方法
        is_mode = button_text in ["程序模式", "网页模式"]  # 判断是否是模式按钮

        # 模式按钮只能二选一
        if is_mode:
            if self.active_mode == button_text:
                self.buttons[button_text].configure(relief="raised", bg="SystemButtonFace")
                self.active_mode = None
                self.drag_data['web_mode'] = False
            else:
                # 取消另一个模式按钮
                for mode in ["程序模式", "网页模式"]:
                    if mode != button_text:
                        self.buttons[mode].configure(relief="raised", bg="SystemButtonFace")
                self.buttons[button_text].configure(relief="sunken", bg=self.get_button_color(button_text))
                self.active_mode = button_text
                self.drag_data['web_mode'] = (button_text == "网页模式")
            # 切换模式时，停止运行
            self.running = False
            self.paused = False
            self.start_btn.configure(relief="raised", bg="lightblue")
            self.pause_btn.configure(relief="raised", bg="lightblue")
            return
        # 只剩下窗口确定按钮
        if button_text == "窗口确定":
            return

    def toggle_control_button(self, button):  # 切换控制按钮状态方法
        # 检查是否选择了模式
        if not self.active_mode:  # 如果没有选择模式
            self.start_btn.configure(relief="raised", bg="lightblue")
            self.pause_btn.configure(relief="raised", bg="lightblue")
            self.log("请先选择模式！")
            return
        # 检查是否锁定了目标位置
        if self.active_mode == "程序模式" and not self.drag_data['target_hwnd']:
            self.start_btn.configure(relief="raised", bg="lightblue")
            self.pause_btn.configure(relief="raised", bg="lightblue")
            self.log("请先选择目标窗口！")
            return
        if self.active_mode == "网页模式" and not self.drag_data['web_position']:
            self.start_btn.configure(relief="raised", bg="lightblue")
            self.pause_btn.configure(relief="raised", bg="lightblue")
            self.log("请先选择目标元素位置！")
            return
        # 启动和暂停按钮只能二选一
        if button == self.start_btn:
            if self.running:
                # 启动按钮复位，程序停止
                self.running = False
                self.paused = False
                self.start_btn.configure(relief="raised", bg="lightblue")
                self.pause_btn.configure(relief="raised", bg="lightblue")
            else:
                # 如果已完成，重新开始，否则继续
                if not self.paused:
                    self.running = True
                    self.paused = False
                    self.current_thread = threading.Thread(target=self.process_numbers)
                    self.current_thread.start()
                else:
                    self.running = True
                    self.paused = False
                self.start_btn.configure(relief="sunken", bg="#9370DB")
                self.pause_btn.configure(relief="raised", bg="lightblue")
        elif button == self.pause_btn:
            if self.paused:
                # 暂停按钮复位，程序停止
                self.running = False
                self.paused = False
                self.start_btn.configure(relief="raised", bg="lightblue")
                self.pause_btn.configure(relief="raised", bg="lightblue")
            else:
                if self.running:
                    self.paused = True
                    self.start_btn.configure(relief="raised", bg="lightblue")
                    self.pause_btn.configure(relief="sunken", bg="#9370DB")

    def highlight_line(self, number):  # 高亮显示行方法
        content = self.input_area.get('1.0', tk.END)  # 获取文本框内容
        lines = content.splitlines()  # 按行分割内容
        for i, line in enumerate(lines):  # 遍历每一行
            if line.strip() == number:  # 如果找到匹配的单号
                start = f"{i + 1}.0"  # 设置起始位置
                end = f"{i + 1}.end"  # 设置结束位置
                self.input_area.tag_add("highlight", start, end)  # 添加高亮标签
                self.input_area.tag_config("highlight", background="#88DB29")  # 设置高亮颜色
                self.input_area.see(start)  # 滚动到高亮行
                break

    def get_delay(self):  # 获取延迟时间方法
        try:
            delay = float(self.speed_delay.get())  # 获取延迟时间
            return delay  # 返回延迟时间
        except ValueError:  # 如果输入无效
            return 0.5  # 返回默认延迟

    def log(self, message):  # 记录日志方法
        self.progress_output.insert(tk.END, f"{message}\n")  # 插入日志信息
        self.progress_output.see(tk.END)  # 滚动到最新日志

    def process_numbers(self):  # 处理单号方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号

        total = len(numbers)  # 获取单号总数
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            return

        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break

                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待

                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签

                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志

                # 根据模式处理单号
                if self.active_mode == "程序模式":
                    self.process_program_mode(num)
                elif self.active_mode == "网页模式":
                    self.process_web_mode(num)

                # 获取延迟时间并等待
                delay_time = self.get_delay()
                time.sleep(delay_time)

        finally:
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.log("处理完成！")

    def process_program_mode(self, number):
        # 处理程序模式下的单号
        try:
            # 获取目标窗口
            hwnd = self.drag_data['target_hwnd']
            if not hwnd:
                self.log("错误: 未锁定目标窗口")
                return

            # 将窗口置于前台
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.2)  # 等待窗口激活

            # 清除现有内容
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.1)
            pyautogui.press('delete')  # 删除
            time.sleep(0.1)

            # 模拟按键输入单号
            pyautogui.typewrite(number)
            time.sleep(0.1)  # 等待输入完成

            # 模拟按回车键提交
            pyautogui.press('enter')

        except Exception as e:
            self.log(f"处理单号时出错: {str(e)}")

    def process_web_mode(self, number):
        # 处理网页模式下的单号
        try:
            # 获取目标位置
            position = self.drag_data['web_position']
            if not position:
                self.log("错误: 未锁定目标元素位置")
                return

            # 移动鼠标到目标位置
            pyautogui.moveTo(position[0], position[1])
            time.sleep(0.2)  # 等待鼠标移动

            # 点击目标位置
            pyautogui.click()
            time.sleep(0.2)  # 等待点击生效

            # 清除现有内容
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.1)
            pyautogui.press('delete')  # 删除
            time.sleep(0.1)

            # 输入单号
            pyautogui.typewrite(number)
            time.sleep(0.1)  # 等待输入完成

            # 模拟按回车键提交
            pyautogui.press('enter')

        except Exception as e:
            self.log(f"处理单号时出错: {str(e)}")



    def start_drag(self, event):  # 开始拖拽方法
        # 记录初始位置
        self.drag_data['dragging'] = True
        # 不再记录按钮位置，因为我们不会移动按钮

        # 如果是网页模式，显示提示
        if self.drag_data['web_mode']:
            self.log("网页模式: 请将鼠标移动到目标元素上，然后松开鼠标标记位置")

    # 移除mark_web_position方法，因为我们不再使用F2热键

    def on_drag(self, event):  # 拖拽中方法
        if not self.drag_data['dragging']:
            return

        # 网页模式下显示靶心大小的圆形黄色蒙版
        if self.drag_data['web_mode']:
            # 获取当前鼠标位置
            screen_x, screen_y = win32api.GetCursorPos()

            # 靶心大小
            target_size = 24

            if self.drag_data['overlay'] is None:
                # 创建靶心大小的覆盖层
                self.drag_data['overlay'] = tk.Toplevel(self.root)
                self.drag_data['overlay'].attributes('-alpha', 0.6)  # 半透明
                self.drag_data['overlay'].attributes('-topmost', True)
                self.drag_data['overlay'].overrideredirect(True)
                self.drag_data['overlay'].configure(bg='black')  # 背景设为黑色
                self.drag_data['overlay'].wm_attributes('-transparentcolor', 'black')  # 设置黑色为透明

                # 创建画布来绘制圆形
                canvas = tk.Canvas(self.drag_data['overlay'], width=target_size, height=target_size,
                                  bg='black', highlightthickness=0)
                canvas.pack()

                # 绘制黄色圆形
                canvas.create_oval(2, 2, target_size-2, target_size-2, fill='#FFEB3B', outline='#FFC107', width=2)

                # 存储画布引用
                self.drag_data['canvas'] = canvas

            # 更新覆盖层位置
            self.drag_data['overlay'].geometry(f"{target_size}x{target_size}+{screen_x-target_size//2}+{screen_y-target_size//2}")
            self.drag_data['overlay'].lift()

            return

        # 获取鼠标所在位置的窗口信息
        screen_x = win32api.GetCursorPos()[0]
        screen_y = win32api.GetCursorPos()[1]
        target_hwnd = win32gui.WindowFromPoint((screen_x, screen_y))

        # 如果找到目标窗口且不是自己
        if target_hwnd and target_hwnd != self.root.winfo_id():
            # 获取窗口位置和大小
            try:
                rect = win32gui.GetWindowRect(target_hwnd)
                if self.drag_data['overlay'] is None:
                    # 创建只有边框的覆盖层
                    self.drag_data['overlay'] = tk.Toplevel(self.root)
                    self.drag_data['overlay'].attributes('-alpha', 1.0)  # 完全不透明
                    self.drag_data['overlay'].attributes('-topmost', True)
                    self.drag_data['overlay'].overrideredirect(True)
                    self.drag_data['overlay'].wm_attributes('-transparentcolor', 'black')  # 设置黑色为透明
                    self.drag_data['overlay'].configure(bg='black')  # 背景设为黑色

                    # 创建一个画布来绘制红色边框
                    canvas = tk.Canvas(self.drag_data['overlay'], bg='black', highlightthickness=0)
                    canvas.pack(fill=tk.BOTH, expand=True)

                    # 将画布存储在字典中以便于更新
                    self.drag_data['canvas'] = canvas

                    self.drag_data['target_hwnd'] = target_hwnd

                # 更新覆盖层位置和大小
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]

                if target_hwnd == self.drag_data['target_hwnd']:
                    self.drag_data['overlay'].geometry(f"{width}x{height}+{rect[0]}+{rect[1]}")
                    # 清除画布并重新绘制边框
                    self.drag_data['canvas'].delete("all")
                    self.drag_data['canvas'].config(width=width, height=height)
                    # 绘制红色边框，线宽为2像素
                    self.drag_data['canvas'].create_rectangle(1, 1, width-1, height-1, outline='#FF4757', width=2)
                    self.drag_data['overlay'].lift()
                else:
                    # 如果鼠标移动到了新窗口，更新目标窗口和覆盖层
                    self.drag_data['target_hwnd'] = target_hwnd
                    self.drag_data['overlay'].geometry(f"{width}x{height}+{rect[0]}+{rect[1]}")
                    # 清除画布并重新绘制边框
                    self.drag_data['canvas'].delete("all")
                    self.drag_data['canvas'].config(width=width, height=height)
                    # 绘制红色边框，线宽为2像素
                    self.drag_data['canvas'].create_rectangle(1, 1, width-1, height-1, outline='#FF4757', width=2)
                    self.drag_data['overlay'].lift()
            except Exception as e:
                self.log(f"创建边框失败: {str(e)}")
        elif self.drag_data['overlay']:
            # 如果没有目标窗口但存在覆盖层，销毁覆盖层
            self.drag_data['overlay'].destroy()
            self.drag_data['overlay'] = None
            self.drag_data['target_hwnd'] = None

    def stop_drag(self, event):  # 停止拖拽方法
        if not self.drag_data['dragging']:
            return

        self.drag_data['dragging'] = False
        button = event.widget

        # 如果是网页模式，直接标记当前鼠标位置
        if self.drag_data['web_mode']:
            # 获取当前鼠标位置
            x, y = pyautogui.position()
            self.drag_data['web_position'] = (x, y)
            self.log(f"已标记网页元素位置: X={x}, Y={y}")

            # 确保蒙版消失
            if self.drag_data['overlay']:
                self.drag_data['overlay'].destroy()
                self.drag_data['overlay'] = None

            return

        # 获取当前鼠标位置和窗口信息
        screen_x, screen_y = win32api.GetCursorPos()
        target_hwnd = win32gui.WindowFromPoint((screen_x, screen_y))

        # 保存目标窗口句柄
        self.drag_data['target_hwnd'] = target_hwnd

        # 输出位置信息
        if target_hwnd:
            try:
                rect = win32gui.GetWindowRect(target_hwnd)
                class_name = win32gui.GetClassName(target_hwnd)
                window_text = win32gui.GetWindowText(target_hwnd)

                # 简化输出信息，只显示类名和窗口句柄
                info = f"窗口类名: {class_name}\n"
                info += f"窗口句柄: {target_hwnd}\n"
                info += f"已锁定目标窗口"

                self.log(info)
            except Exception as e:
                self.log(f"获取窗口信息失败: {str(e)}")

        # 不需要恢复按钮位置，因为我们没有移动按钮

        # 销毁覆盖层
        if self.drag_data['overlay']:
            self.drag_data['overlay'].destroy()
            self.drag_data['overlay'] = None

if __name__ == "__main__":
    try:
        # 尝试导入必要的库
        import pyautogui
    except ImportError:
        # 如果导入失败，显示错误消息
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        messagebox.showerror("缺少必要的库",
                           "请安装必要的库\n\npip install pywin32 pyautogui")
        exit(1)

    root = tk.Tk()
    app = AutomationTool(root)
    root.mainloop()
