import tkinter as tk
from tkinter import ttk
import pyautogui
import time

class InputTool:
    def __init__(self, root):
        self.root = root
        self.root.title("单号输入工具")
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建文本输入框
        self.text_input = tk.Text(main_frame, width=40, height=10)
        self.text_input.grid(row=0, column=0, padx=5, pady=5)
        
        # 创建坐标设置框架
        coord_frame = ttk.LabelFrame(main_frame, text="目标坐标设置", padding="5")
        coord_frame.grid(row=1, column=0, pady=5, sticky=(tk.W, tk.E))
        
        # X坐标输入
        ttk.Label(coord_frame, text="X:").grid(row=0, column=0)
        self.x_coord = ttk.Entry(coord_frame, width=10)
        self.x_coord.grid(row=0, column=1, padx=5)
        self.x_coord.insert(0, "500")
        
        # Y坐标输入
        ttk.Label(coord_frame, text="Y:").grid(row=0, column=2)
        self.y_coord = ttk.Entry(coord_frame, width=10)
        self.y_coord.grid(row=0, column=3, padx=5)
        self.y_coord.insert(0, "500")
        
        # 获取坐标按钮
        self.get_pos_btn = ttk.Button(coord_frame, text="获取当前坐标", command=self.get_current_position)
        self.get_pos_btn.grid(row=0, column=4, padx=5)
        
        # 创建延迟设置框架
        delay_frame = ttk.LabelFrame(main_frame, text="延迟设置(毫秒)", padding="5")
        delay_frame.grid(row=2, column=0, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Label(delay_frame, text="输入延迟:").grid(row=0, column=0, padx=5)
        self.input_delay = ttk.Entry(delay_frame, width=10)
        self.input_delay.grid(row=0, column=1, padx=5)
        self.input_delay.insert(0, "500")
        
        # 创建启动按钮
        self.start_btn = ttk.Button(main_frame, text="启动", command=self.start_input)
        self.start_btn.grid(row=3, column=0, pady=10)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪")
        self.status_label.grid(row=4, column=0, pady=5)

    def get_current_position(self):
        self.status_label.config(text="请在3秒内将鼠标移动到目标位置...")
        self.root.update()
        time.sleep(3)
        
        x, y = pyautogui.position()
        self.x_coord.delete(0, tk.END)
        self.y_coord.delete(0, tk.END)
        self.x_coord.insert(0, str(x))
        self.y_coord.insert(0, str(y))
        self.status_label.config(text=f"已获取坐标: ({x}, {y})")

    def start_input(self):
        # 获取输入的文本
        text = self.text_input.get("1.0", tk.END).strip()
        if not text:
            self.status_label.config(text="请输入单号！")
            return
        
        # 获取坐标
        try:
            x = int(self.x_coord.get())
            y = int(self.y_coord.get())
        except ValueError:
            self.status_label.config(text="请输入有效的坐标！")
            return
            
        # 获取延迟设置（毫秒转秒）
        try:
            delay = int(self.input_delay.get()) / 1000
        except ValueError:
            self.status_label.config(text="请输入有效的延迟时间！")
            return
        
        # 给用户5秒时间切换到目标窗口
        self.status_label.config(text="请在5秒内切换到目标窗口...")
        self.root.update()
        time.sleep(5)
        
        # 开始输入
        numbers = text.split('\n')
        for i, number in enumerate(numbers):
            if number.strip():  # 跳过空行
                # 点击坐标位置
                pyautogui.click(x, y)
                time.sleep(0.5)
                # 清除可能存在的文本
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.1)
                # 输入新的单号
                pyautogui.write(number.strip())
                time.sleep(delay)  # 使用自定义延迟
                # 按回车确认
                pyautogui.press('enter')
                time.sleep(delay)  # 使用自定义延迟
                
                self.status_label.config(text=f"正在处理: {i+1}/{len(numbers)}")
                self.root.update()
        
        self.status_label.config(text="处理完成！")

if __name__ == "__main__":
    root = tk.Tk()
    app = InputTool(root)
    root.mainloop()