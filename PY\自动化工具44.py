
import tkinter as tk  # 导入tkinter库
from tkinter import ttk  # 导入ttk模块
from tkinter import scrolledtext  # 导入scrolledtext模块
import uiautomator2 as u2  # 导入uiautomator2库
import time  # 导入time模块
import random  # 导入random模块
import threading  # 导入threading模块
from uiautomator2 import UiObject  # 导入UiObject，用于显示等待

class AutomationTool:  # 定义AutomationTool类
    def __init__(self, root):  # 初始化方法
        self.root = root  # 设置根窗口
        self.root.title("自动化工具")  # 设置窗口标题
        self.root.geometry("480x640")  # 设置窗口大小
        self.root.resizable(False, False)  # 设置窗口不可调整大小
        
        # 定义统一字体样式
        self.font_style = ('楷体', 12)  # 设置字体样式
        
        # 创建主框架
        self.main_frame = tk.Frame(root)  # 创建主框架
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)  # 放置主框架
        
        # 创建左右框架
        self.left_frame = tk.Frame(self.main_frame, width=240)  # 创建左框架
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=2)  # 放置左框架
        self.left_frame.pack_propagate(False)  # 禁止左框架自动调整大小
        
        self.right_frame = tk.Frame(self.main_frame, width=240)  # 创建右框架
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=2)  # 放置右框架
        
        # 创建单号输入区域
        self.input_area = scrolledtext.ScrolledText(  # 创建滚动文本框
            self.left_frame, 
            width=25, 
            height=25,
            font=self.font_style  # 设置字体样式
        )
        self.input_area.pack(fill=tk.BOTH, expand=True)  # 放置滚动文本框
        
        # 创建进度条
        self.progress_frame = tk.Frame(self.left_frame)  # 创建进度条框架
        self.progress_frame.pack(fill=tk.X, pady=5)  # 放置进度条框架
        
        self.progress = ttk.Progressbar(self.progress_frame, mode='determinate')  # 创建进度条
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)  # 放置进度条
        
        # 设置进度条颜色
        style = ttk.Style()  # 创建样式对象
        style.configure("Horizontal.TProgressbar", background='#88DB29')  # 配置进度条颜色
        self.progress.configure(style="Horizontal.TProgressbar")  # 应用样式
        
        self.progress_label = tk.Label(self.progress_frame, text="0%", font=self.font_style)  # 创建进度标签
        self.progress_label.pack(side=tk.LEFT, padx=5)  # 放置进度标签
        
        # 创建进度输出框
        self.progress_output = scrolledtext.ScrolledText(  # 创建滚动文本框
            self.left_frame, 
            height=6,
            font=self.font_style  # 设置字体样式
        )
        self.progress_output.pack(fill=tk.X)  # 放置滚动文本框
        
        # 初始化变量
        self.buttons = {}  # 初始化按钮字典
        self.active_mode = None  # 初始化当前模式
        self.active_function = None  # 初始化当前功能
        self.running = False  # 初始化运行状态
        self.paused = False  # 初始化暂停状态
        self.current_thread = None  # 初始化当前线程
        
        # 创建界面元素
        self.create_buttons()  # 创建按钮
        self.create_speed_control()  # 创建速度控制
        self.create_control_buttons()  # 创建控制按钮

    def create_buttons(self):  # 创建按钮方法
        # 模式按钮
        mode_frame = tk.Frame(self.right_frame)  # 创建模式按钮框架
        mode_frame.pack(fill=tk.X, pady=10)  # 放置模式按钮框架
        
        button_style = {  # 定义按钮样式
            'width': 12,
            'height': 2,
            'font': self.font_style,
            'relief': 'raised',
            'bd': 2
        }
        
        # 创建模式按钮
        mode_buttons = ["顺序模式", "随机模式"]  # 定义模式按钮文本
        for mode in mode_buttons:  # 遍历模式按钮
            btn = tk.Button(mode_frame, text=mode, **button_style)  # 创建按钮
            btn.configure(command=lambda m=mode: self.toggle_button(m))  # 设置按钮命令
            btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置按钮
            self.buttons[mode] = btn  # 将按钮添加到按钮字典

        # 功能按钮，按新的顺序排列
        functions = [  # 定义功能按钮文本
            ("中通揽发", "中通建包"),
            ("兔喜入库", "兔喜出库"),
            ("韵达揽收", "韵达乡镇"),
            ("韵达集包", "韵超入库"),
            ("韵超出库", "多多出库"),
            ("多多入库", None)
        ]
        
        button_frame = tk.Frame(self.right_frame)  # 创建功能按钮框架
        button_frame.pack(fill=tk.BOTH, expand=True, pady=10)  # 放置功能按钮框架
        
        for left_text, right_text in functions:  # 遍历功能按钮
            frame = tk.Frame(button_frame)  # 创建功能按钮子框架
            frame.pack(fill=tk.X, pady=8)  # 放置功能按钮子框架
            
            left_btn = tk.Button(frame, text=left_text, **button_style)  # 创建左侧按钮
            left_btn.configure(command=lambda btn=left_text: self.toggle_button(btn))  # 设置左侧按钮命令
            left_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置左侧按钮
            self.buttons[left_text] = left_btn  # 将左侧按钮添加到按钮字典
            
            if right_text:  # 只有当right_text不为None时才创建右按钮
                right_btn = tk.Button(frame, text=right_text, **button_style)  # 创建右侧按钮
                right_btn.configure(command=lambda btn=right_text: self.toggle_button(btn))  # 设置右侧按钮命令
                right_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置右侧按钮
                self.buttons[right_text] = right_btn  # 将右侧按钮添加到按钮字典

    def create_speed_control(self):  # 创建速度控制方法
        speed_frame = tk.Frame(self.right_frame)  # 创建速度控制框架
        speed_frame.pack(fill=tk.X, pady=10)  # 放置速度控制框架
        
        tk.Label(  # 创建标签
            speed_frame, 
            text="延迟时间范围（秒）", 
            font=self.font_style  # 设置字体样式
        ).pack()  # 放置标签
        
        control_frame = tk.Frame(speed_frame)  # 创建控制框架
        control_frame.pack(pady=5)  # 放置控制框架
        
        # 添加最小延迟标签和输入框
        tk.Label(  # 创建最小延迟标签
            control_frame, 
            text="最小:", 
            font=self.font_style  # 设置字体样式
        ).pack(side=tk.LEFT, padx=2)  # 放置最小延迟标签
        
        self.speed_min = tk.Spinbox(  # 创建最小延迟输入框
            control_frame, 
            from_=0, 
            to=60,
            increment=0.1, 
            width=6, 
            font=self.font_style  # 设置字体样式
        )
        self.speed_min.pack(side=tk.LEFT, padx=2)  # 放置最小延迟输入框
        
        # 添加最大延迟标签和输入框
        tk.Label(  # 创建最大延迟标签
            control_frame, 
            text="最大:", 
            font=self.font_style  # 设置字体样式
        ).pack(side=tk.LEFT, padx=2)  # 放置最大延迟标签
        
        self.speed_max = tk.Spinbox(  # 创建最大延迟输入框
            control_frame, 
            from_=0, 
            to=60,
            increment=0.1, 
            width=6, 
            font=self.font_style  # 设置字体样式
        )
        self.speed_max.pack(side=tk.LEFT, padx=2)  # 放置最大延迟输入框
        
        # 添加说明文字
        tk.Label(  # 创建说明文字标签
            speed_frame, 
            text="不设置则使用默认延迟", 
            font=('楷体', 10), 
            fg='gray'  # 设置字体颜色
        ).pack(pady=(2, 0))  # 放置说明文字标签

    def create_control_buttons(self):  # 创建控制按钮方法
        control_frame = tk.Frame(self.right_frame)  # 创建控制按钮框架
        control_frame.pack(fill=tk.X, pady=10)  # 放置控制按钮框架
        
        control_button_style = {  # 定义控制按钮样式
            'width': 12,
            'height': 2,
            'font': self.font_style,
            'relief': 'raised',
            'bd': 2
        }
        
        self.start_btn = tk.Button(control_frame, text="启动", bg='lightblue', **control_button_style)  # 创建启动按钮
        self.start_btn.configure(command=lambda: self.toggle_control_button(self.start_btn))  # 设置启动按钮命令
        self.start_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置启动按钮
        
        self.pause_btn = tk.Button(control_frame, text="暂停", bg='lightblue', **control_button_style)  # 创建暂停按钮
        self.pause_btn.configure(command=lambda: self.toggle_control_button(self.pause_btn))  # 设置暂停按钮命令
        self.pause_btn.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)  # 放置暂停按钮

    def get_button_color(self, button_text):  # 获取按钮颜色方法
        if button_text in ["顺序模式", "随机模式"]:  # 如果是模式按钮
            return "#00FFFF"  # 返回青色
        elif button_text in ["中通揽发", "中通建包"]:  # 如果是中通按钮
            return "#1E90FF"  # 返回道奇蓝
        elif button_text in ["兔喜入库", "兔喜出库"]:  # 如果是兔喜按钮
            return "#1E90FF"  # 返回道奇蓝
        elif button_text in ["韵达揽收", "韵达乡镇", "韵达集包", "韵超入库", "韵超出库"]:  # 如果是韵达按钮
            return "#FFD700"  # 返回金色
        elif button_text in ["多多入库", "多多出库"]:  # 如果是多多按钮
            return "#FF6B6B"  # 返回红色
        return "SystemButtonFace"  # 返回默认按钮颜色

    def toggle_button(self, button_text):  # 切换按钮状态方法
        is_mode = button_text in ["顺序模式", "随机模式"]  # 判断是否是模式按钮
        
        # 韵达乡镇和随机模式互斥
        if button_text == "随机模式" and self.active_function == "韵达乡镇":  # 如果是随机模式且当前功能是韵达乡镇
            self.log("韵达乡镇不支持随机模式！")  # 记录日志
            return
        elif button_text == "韵达乡镇" and self.active_mode == "随机模式":  # 如果是韵达乡镇且当前模式是随机模式
            self.log("韵达乡镇不支持随机模式！")  # 记录日志
            return
        
        # 如果按钮已激活
        if ((is_mode and self.active_mode == button_text) or 
            (not is_mode and self.active_function == button_text)):
            self.buttons[button_text].configure(relief="raised", bg="SystemButtonFace")  # 恢复按钮状态
            if is_mode:  # 如果是模式按钮
                self.active_mode = None  # 清空当前模式
            else:  # 如果是功能按钮
                self.active_function = None  # 清空当前功能
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.pause_btn.configure(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
            return

        for btn_text, btn in self.buttons.items():  # 遍历所有按钮
            # 如果是模式按钮或功能按钮
            if ((is_mode and btn_text in ["顺序模式", "随机模式"]) or 
               (not is_mode and btn_text not in ["顺序模式", "随机模式"])):
                btn.configure(relief="raised", bg="SystemButtonFace")  # 恢复按钮状态
        
        self.buttons[button_text].configure(relief="sunken", bg=self.get_button_color(button_text))  # 设置按钮状态
        
        if is_mode:  # 如果是模式按钮
            self.active_mode = button_text  # 设置当前模式
            if self.active_function:  # 如果有当前功能
                self.buttons[self.active_function].configure(  # 设置功能按钮状态
                    relief="sunken", 
                    bg=self.get_button_color(self.active_function)
                )
        else:  # 如果是功能按钮
            self.active_function = button_text  # 设置当前功能
            if self.active_mode:  # 如果有当前模式
                self.buttons[self.active_mode].configure(  # 设置模式按钮状态
                    relief="sunken", 
                    bg=self.get_button_color(self.active_mode)
                )

    def reset_interface(self):  # 重置界面方法
        # 清空输入框
        self.input_area.delete('1.0', tk.END)  # 清空输入框内容
        # 清空输出框
        self.progress_output.delete('1.0', tk.END)  # 清空输出框内容
        # 进度条归零
        self.progress['value'] = 0  # 重置进度条
        self.progress_label.config(text="0%")  # 重置进度标签
        # 重置按钮状态
        for btn_text, btn in self.buttons.items():  # 遍历所有按钮
            btn.configure(relief="raised", bg="SystemButtonFace")  # 恢复按钮状态
        self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
        self.pause_btn.configure(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
        # 重置状态变量
        self.active_mode = None  # 清空当前模式
        self.active_function = None  # 清空当前功能
        self.running = False  # 停止运行

    def toggle_control_button(self, button):  # 切换控制按钮状态方法
        if not (self.active_mode and self.active_function):  # 如果没有选择模式和功能
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            self.pause_btn.configure(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
            self.log("请先选择模式和功能！")  # 记录日志
            return
            
        if button.cget('relief') == 'sunken':  # 如果按钮已按下
            if button == self.start_btn:  # 如果是启动按钮
                self.running = False  # 停止运行
                self.reset_interface()  # 重置界面
            else:  # 如果是暂停按钮
                self.paused = False  # 取消暂停
            button.configure(relief="raised", bg="lightblue")  # 恢复按钮状态
        else:  # 如果按钮未按下
            if button == self.start_btn:  # 如果是启动按钮
                if not self.running:  # 如果未运行
                    self.running = True  # 开始运行
                    self.paused = False  # 取消暂停
                    if self.active_function == "多多入库":  # 如果是多多入库功能
                        self.current_thread = threading.Thread(target=self.run_duoduo_inbound)  # 创建线程
                    elif self.active_function == "多多出库":  # 如果是多多出库功能
                        self.current_thread = threading.Thread(target=self.run_duoduo_outbound)  # 创建线程
                    elif self.active_function == "中通建包":  # 如果是中通建包功能
                        self.current_thread = threading.Thread(target=self.run_zhongtong_build)  # 创建线程
                    elif self.active_function == "韵达集包":  # 如果是韵达集包功能
                        self.current_thread = threading.Thread(target=self.run_yunda_build)  # 创建线程
                    elif self.active_function == "韵达乡镇":  # 如果是韵达乡镇功能
                        self.current_thread = threading.Thread(target=self.run_yunda_township)  # 创建线程
                    elif self.active_function == "中通揽发":  # 如果是中通揽发功能
                        self.current_thread = threading.Thread(target=self.run_zhongtong_pickup)  # 创建线程
                    elif self.active_function == "韵达揽收":  # 如果是韵达揽收功能
                        self.current_thread = threading.Thread(target=self.run_yunda_pickup)  # 创建线程
                    elif self.active_function == "兔喜入库":  # 如果是兔喜入库功能
                        self.current_thread = threading.Thread(target=self.run_tuxi_inbound)  # 创建线程
                    self.current_thread.start()  # 启动线程
            else:  # 如果是暂停按钮
                self.paused = True  # 暂停运行
            
            if button == self.start_btn:  # 如果是启动按钮
                self.pause_btn.configure(relief="raised", bg="lightblue")  # 恢复暂停按钮状态
            else:  # 如果是暂停按钮
                self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态
            button.configure(relief="sunken", bg="#9370DB")  # 设置按钮状态

    def highlight_line(self, number):  # 高亮显示行方法
        content = self.input_area.get('1.0', tk.END)  # 获取文本框内容
        lines = content.splitlines()  # 按行分割内容
        for i, line in enumerate(lines):  # 遍历每一行
            if line.strip() == number:  # 如果找到匹配的单号
                start = f"{i + 1}.0"  # 设置起始位置
                end = f"{i + 1}.end"  # 设置结束位置
                self.input_area.tag_add("highlight", start, end)  # 添加高亮标签
                self.input_area.tag_config("highlight", background="#88DB29")  # 设置高亮颜色
                self.input_area.see(start)  # 滚动到高亮行
                break

    def get_delay_range(self):  # 获取延迟范围方法
        try:
            min_delay = float(self.speed_min.get())  # 获取最小延迟
            max_delay = float(self.speed_max.get())  # 获取最大延迟
            if min_delay == 0 and max_delay == 0:  # 如果未设置延迟
                return None
            return (min_delay, max_delay)  # 返回延迟范围
        except ValueError:  # 如果输入无效
            return None

    def log(self, message):  # 记录日志方法
        self.progress_output.insert(tk.END, f"{message}\n")  # 插入日志信息
        self.progress_output.see(tk.END)  # 滚动到最新日志

    def wait_and_click(self, d: u2.Device, resource_id: str, timeout: float = 1.0) -> bool:
        """
        等待元素出现并点击。

        Args:
            d: uiautomator2 设备对象。
            resource_id: 要等待和点击的元素的 resource_id。
            timeout: 等待超时时间(秒)。

        Returns:
            如果元素出现并成功点击，则返回 True；否则返回 False。
        """
        element: UiObject = d(resourceId=resource_id)
        if element.exists(timeout=timeout):
            element.click()
            return True
        return False

    def run_duoduo_inbound(self):  # 多多入库方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号
        
        total = len(numbers)  # 获取单号总数
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return
            
        if self.active_mode == "随机模式":  # 如果是随机模式
            random.shuffle(numbers)  # 随机打乱单号顺序
            
        d = u2.connect()  # 连接设备
        d.set_input_ime(True)  # 启用输入法
        
        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break
                    
                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待
                    
                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签
                
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志
                
                d(resourceId="com.xunmeng.station:id/et_with_delete", text="运单号").set_text(num)  # 输入运单号
                time.sleep(0.1)  # 等待
                d.press("back")  # 按下返回键

                d(resourceId="com.xunmeng.station:id/et_with_delete", text="手机号").set_text("13800138000")  # 输入手机号
                self.log("输入固定号码")  # 记录日志
                time.sleep(0.1)  # 等待
                d.press("back")  # 按下返回键
                
                d(resourceId="com.xunmeng.station:id/tv_scan_confirm").click()  # 点击确认按钮
                
                delay_range = self.get_delay_range()  # 获取延迟范围
                if delay_range:  # 如果有延迟范围
                    time.sleep(random.uniform(delay_range[0], delay_range[1]))  # 随机延迟
                else:  # 如果没有延迟范围
                    time.sleep(0.1)  # 默认延迟
                    
        finally:
            d.set_input_ime(False)  # 禁用输入法
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态

    def run_duoduo_outbound(self):  # 多多出库方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号
        
        total = len(numbers)  # 获取单号总数
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return
            
        if self.active_mode == "随机模式":  # 如果是随机模式
            random.shuffle(numbers)  # 随机打乱单号顺序
            
        d = u2.connect()  # 连接设备
        
        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break
                    
                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待
                    
                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签
                
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志
                
                d(resourceId="com.xunmeng.station:id/et_with_delete").set_text(num)  # 输入单号
                time.sleep(0.1)  # 等待
                
                d.click(606, 1196)  # 点击屏幕坐标
                
                delay_range = self.get_delay_range()  # 获取延迟范围
                if delay_range:  # 如果有延迟范围
                    delay_time = random.uniform(delay_range[0], delay_range[1])  # 随机延迟
                    self.log(f"随机延迟 {delay_time:.2f} 秒")  # 记录日志
                    time.sleep(delay_time)  # 等待
                
                error_hint = d(resourceId="com.xunmeng.station:id/tv_error_hint")  # 检查错误提示
                if error_hint.exists:  # 如果存在错误提示
                    self.log("此单号不在库")  # 记录日志
                    continue
                    
                title_button = d(resourceId="com.xunmeng.station:id/tv_confirm_pop_v2")  # 检查确认按钮
                if title_button.exists:  # 如果存在确认按钮
                    title_button.click()  # 点击确认按钮
                    self.log("出库成功")  # 记录日志
                    
        finally:
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态

    def run_zhongtong_build(self):  # 中通建包方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号
        
        total = len(numbers)  # 获取单号总数
        success = 0  # 初始化成功计数
        
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return
            
        if self.active_mode == "随机模式":  # 如果是随机模式
            random.shuffle(numbers)  # 随机打乱单号顺序
            
        d = u2.connect()  # 连接设备
        d.set_input_ime(True)  # 启用输入法
        
        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break
                    
                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待
                    
                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签
                
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志
                
                d(text="请输入单号或包号或环保袋").set_text(num)  # 输入单号
                d(resourceId="com.zto.pdaunity:id/btn_done").click()  # 点击完成按钮
                success += 1  # 增加成功计数
                
                delay_range = self.get_delay_range()  # 获取延迟范围
                if delay_range:  # 如果有延迟范围
                    time.sleep(random.uniform(delay_range[0], delay_range[1]))  # 随机延迟
                else:  # 如果没有延迟范围
                    time.sleep(0.5)  # 默认延迟
                    
            self.log("所有单号输入完成！")  # 记录日志
                    
        finally:
            d.set_input_ime(False)  # 禁用输入法
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态

    def run_yunda_build(self):  # 韵达集包方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号
        
        total = len(numbers)  # 获取单号总数
        success = 0  # 初始化成功计数
        
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return
            
        if self.active_mode == "随机模式":  # 如果是随机模式
            random.shuffle(numbers)  # 随机打乱单号顺序
            
        d = u2.connect()  # 连接设备
        d.set_input_ime(True)  # 启用输入法
        
        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break
                    
                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待
                    
                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签
                
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志
                
                # 先处理弹窗（最多等待0.05秒）
                if d(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                    d(resourceId="android:id/button1").click()  # 点击确定按钮
                    self.log("已确定")  # 记录日志
                
                # 输入单号
                d(resourceId="com.yd.expresswebsite:id/took_shipment_collection_bill_number").set_text(num)  # 输入单号
                d(text="添加").click()  # 点击添加按钮
                success += 1  # 增加成功计数
                
                delay_range = self.get_delay_range()  # 获取延迟范围
                if delay_range:  # 如果有延迟范围
                    time.sleep(random.uniform(delay_range[0], delay_range[1]))  # 随机延迟
                else:  # 如果没有延迟范围
                    time.sleep(0.01)  # 默认延迟
                    
            self.log("所有单号输入完成！")  # 记录日志
                    
        finally:
            d.set_input_ime(False)  # 禁用输入法
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态

    def run_yunda_township(self):  # 韵达乡镇方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号
        
        total = len(numbers)  # 获取单号总数
        success = 0  # 初始化成功计数
        
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return
            
        d = u2.connect()  # 连接设备
        d.set_input_ime(True)  # 启用输入法
        
        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break
                    
                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待
                    
                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签
                
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志
                
                # 先处理弹窗（最多等待0.1秒）
                if d(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                    d(resourceId="android:id/button1").click()  # 点击确定按钮
                    self.log("已确定")  # 记录日志

                if d(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                    d(resourceId="android:id/button1").click()  # 点击确定按钮
                    self.log("已确定")  # 记录日志                    
                
                if len(num) < 6:  # 如果单号长度小于6
                    # 输入到站点编码输入框
                    d(resourceId="com.yd.expresswebsite:id/shipment_business_shipment_give_shipment_station_code").set_text(num)  # 输入站点编码
                    # 输入站点编码后处理弹窗
                    if d(resourceId="android:id/button1").exists(timeout=0.05):  # 如果存在弹窗
                        d(resourceId="android:id/button1").click()  # 点击确定按钮
                        self.log("已确定")  # 记录日志
                else:  # 如果单号长度大于等于6
                    # 输入到单号输入框
                    d(resourceId="com.yd.expresswebsite:id/shipment_business_shipment_give_shipment_bill_number").set_text(num)  # 输入单号
                
                # 点击添加按钮
                d(text="添加").click()  # 点击添加按钮
                success += 1  # 增加成功计数
                
                delay_range = self.get_delay_range()  # 获取延迟范围
                if delay_range:  # 如果有延迟范围
                    time.sleep(random.uniform(delay_range[0], delay_range[1]))  # 随机延迟
                else:  # 如果没有延迟范围
                    time.sleep(0.03)  # 默认延迟
                    
            self.log("所有单号输入完成！")  # 记录日志
                    
        finally:
            d.set_input_ime(False)  # 禁用输入法
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态

    def run_zhongtong_pickup(self):  # 中通揽发方法
        # 中通揽发功能待实现
        pass

    def run_yunda_pickup(self):  # 韵达揽收方法
        # 韵达揽收功能待实现
        pass

    def run_tuxi_inbound(self):  # 兔喜入库方法
        numbers = self.input_area.get('1.0', tk.END).splitlines()  # 获取单号列表
        numbers = [num.strip() for num in numbers if num.strip()]  # 清理单号
        
        total = len(numbers)  # 获取单号总数
        success = 0  # 初始化成功计数
        
        if not total:  # 如果没有单号
            self.log("没有找到有效的单号")  # 记录日志
            return
            
        if self.active_mode == "随机模式":  # 如果是随机模式
            random.shuffle(numbers)  # 随机打乱单号顺序
            
        d = u2.connect()  # 连接设备
        d.set_input_ime(True)  # 启用输入法

        # 定义点击"自提"的函数
        def click_pickup_if_exists(d: u2.Device, timeout: float = 0.3) -> bool:
            """等待并点击“自提”弹窗，如果存在。"""
            pickup_element: UiObject = d(resourceId="com.zto.families.ztofamilies:id/tv_three_two")
            if pickup_element.exists(timeout=timeout):
                pickup_element.click()
                self.log("自提确认")
                return True
            return False

        # 定义点击"继续入库"的函数
        def click_continue_if_exists(d: u2.Device, timeout: float = 0.3) -> bool:
            """等待并点击“继续入库”弹窗，如果存在。"""
            continue_element: UiObject = d(resourceId="com.zto.families.ztofamilies:id/tv_cancel")
            if continue_element.exists(timeout=timeout):
                continue_element.click()
                self.log("继续入库")
                return True
            return False

        # 定义点击"送货上门"的函数
        def click_delivery_if_exists(d: u2.Device, timeout: float = 0.3) -> bool:
            """等待并点击“送货上门”弹窗，如果存在。""" 
            delivery_element: UiObject = d(resourceId="com.zto.families.ztofamilies:id/tv_sure")
            if delivery_element.exists(timeout=timeout):
                delivery_element.click() 
                self.log("送货上门")
                return True
            return False
        
        try:
            for index, num in enumerate(numbers):  # 遍历单号
                if not self.running:  # 如果停止运行
                    break
                    
                while self.paused:  # 如果暂停
                    time.sleep(0.1)  # 等待
                    
                progress = (index + 1) / total * 100  # 计算进度
                self.progress['value'] = progress  # 更新进度条
                self.progress_label.config(text=f"{int(progress)}%")  # 更新进度标签
                
                self.highlight_line(num)  # 高亮显示当前单号
                self.log(f"总数: {total}, 进度: {index + 1}/{total}, 当前单号: {num}")  # 记录日志

                # 将单号输入到指定的输入框
                d(resourceId="com.zto.families.ztofamilies:id/edit_storage_wb").set_text(num)
                time.sleep(0.02)  # 等待输入完成

                # 查找并点击手机号输入框
                d(resourceId="com.zto.families.ztofamilies:id/edit_phone_num").click()

                # 定义补全输入框的ID列表
                completion_field_ids = [
                    "com.zto.families.ztofamilies:id/edit_front_phone_num_completion_one",
                    "com.zto.families.ztofamilies:id/edit_front_phone_num_completion_two",
                    "com.zto.families.ztofamilies:id/edit_front_phone_num_completion_third",
                    "com.zto.families.ztofamilies:id/edit_front_phone_num_completion_four"
                ]

                # 检查第一个补全框是否存在
                if d(resourceId=completion_field_ids[0]).exists(timeout=0.3):
                    # 如果存在，给所有四个补全框输入4
                    self.log("补全号码")
                    d(resourceId=completion_field_ids[0]).set_text("4")
                    time.sleep(0.01)
                    d(resourceId=completion_field_ids[1]).set_text("4")
                    time.sleep(0.01)
                    d(resourceId=completion_field_ids[2]).set_text("4")
                    time.sleep(0.01)
                    d(resourceId=completion_field_ids[3]).set_text("4")
                    time.sleep(0.01)
                    # 如果出现补全情况，点击单号输入框以隐藏键盘或确认
                    d(resourceId="com.zto.families.ztofamilies:id/edit_pick_up_code").click()
                else:
                    # 如果不存在，执行原有的手机号输入逻辑
                    phone_num_element = d(resourceId="com.zto.families.ztofamilies:id/edit_phone_num")
                    if phone_num_element.exists(timeout=0.3): # 检查原始输入框是否存在
                        phone_num_element.set_text("13800138000")
                        self.log("输入固定号码")
                time.sleep(0.2)  # 等待输入完成

                # 处理弹窗 (使用显示等待)
                click_delivery_if_exists(d)
                click_pickup_if_exists(d)
                click_continue_if_exists(d)
            
                success += 1  # 增加成功计数

                delay_range = self.get_delay_range()  # 获取延迟范围
                if delay_range:  # 如果有延迟范围
                    time.sleep(random.uniform(delay_range[0], delay_range[1]))  # 随机延迟
                else:  # 如果没有延迟范围
                    time.sleep(0.1)  # 默认延迟
                    
            self.log("所有单号输入完成！")  # 记录日志
                    
        finally:
            d.set_input_ime(False)  # 禁用输入法
            self.running = False  # 停止运行
            self.start_btn.configure(relief="raised", bg="lightblue")  # 恢复启动按钮状态

if __name__ == "__main__":  # 主程序入口
    root = tk.Tk()  # 创建根窗口
    app = AutomationTool(root)  # 创建AutomationTool实例
    root.mainloop()  # 进入主事件循环
